import { MouseEvent, useCallback, useEffect, useState } from 'react';

import Button from '~/components/global/Button/Button';
import Loading from '~/components/global/Loading/Loading';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { AddToCartParams } from '~/components/modules/Cart/CartUserAction.types';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { OrderProduct } from '~/data/models/OrderProduct';
import { OrderTrackingInput } from '~/data/models/OrderTrackingInput';
import { ReturnRequestInput } from '~/data/models/ReturnRequestInput';
import useBreakpoints from '~/hooks/useBreakpoints';
import { BUTTON_STYLE } from '~/lib/constants/buttons.types';
import { SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import { THEME } from '~/lib/constants/theme';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { seStorage } from '~/lib/utils/browser-storage';
import { ui } from '~/lib/utils/ui-dictionary';

import { getDefaultQuantity } from '../../ProductDetail/ProductDetail.utils';
import OrderDetails from '../OrderDetails/OrderDetails';
import styles from './OrderItem.styles';

interface RequestType {
  type: string;
}
type ReturnRequestProps = ReturnRequestInput & RequestType;
type ReturnReasonDataProps = OrderProduct & OrderTrackingInput;

interface Props {
  getReturnReasons: ({
    productId,
    image,
    name,
    quantity,
    zip,
    orderId,
    brand,
  }: ReturnReasonDataProps) => void;
  isLoadingReturnReasons: boolean;
  isSendingReturnOrCancelReq: boolean;
  orderId: number;
  orderUuid: string;
  returnOrCancelReqSent: boolean;
  sendReturnRequest: ({
    orderId,
    zip,
    productId,
    body,
  }: ReturnRequestProps) => void;
  zip: string;
}
type OrderItemProps = OrderProduct & Props;

function OrderItem({
  brand,
  price,
  extendedPrice,
  image,
  name,
  quantity,
  returnQuantity,
  steerUrl,
  productId,
  canCustomerReorder,
  zip,
  sku,
  line,
  category,
  variant,
  orderId,
  orderUuid,
  canCustomerCancelReturn,
  canCustomerReturn,
  isLoadingReturnReasons,
  getReturnReasons,
  sendReturnRequest,
  isSendingReturnOrCancelReq,
  returnOrCancelReqSent,
  poNumber,
  resolveLoanId,
}: OrderItemProps) {
  const [activeItemID, setActiveItemID] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingFromCatalog, setIsAddingFromCatalog] = useState(false);

  const { addToCart, isAddingToCart } = useCartUserActionContextSelector(
    (v) => ({ addToCart: v.addToCart, isAddingToCart: v.isAddingToCart }),
  );

  const isDealerTire = useUserPersonalizationContextSelector(
    (v) => v.isDealerTire,
  );
  const isOTS = useGlobalsContextSelector((v) => Number(v.isOTS) === 1);

  const { isTablet } = useBreakpoints();

  const { cartSummary, setIsCartSummaryModalOpen } =
    useCartSummaryContextSelector((v) => ({
      cartSummary: v.cartSummary,
      setIsCartSummaryModalOpen: v.setIsCartSummaryModalOpen,
    }));

  const handleReturnBtnClick = (event: MouseEvent) => {
    setActiveItemID(productId);
    event.preventDefault();
    const productData = {
      brand,
      canCustomerCancelReturn,
      canCustomerReorder,
      canCustomerReturn,
      category,
      extendedPrice,
      image,
      line,
      name,
      orderId: String(orderId),
      price,
      productId,
      quantity,
      returnQuantity,
      sku,
      steerUrl,
      variant,
      zip,
    };
    getReturnReasons({
      ...productData,
    });
  };

  const handleAddToCart = useCallback(
    async (event: MouseEvent) => {
      setIsLoading(true);
      event.preventDefault();
      if (!productId) {
        return;
      }
      const quantity = getDefaultQuantity(null, 4);
      let addToCartParams: AddToCartParams = {
        productId: String(productId),
        quantity,
        shouldAddCoverage: false,
      };
      const preSelectedInstaller = seStorage.getItem(
        SESSION_STORAGE.INSTALLER_ID_SELECTED,
      );
      if (preSelectedInstaller && !cartSummary?.installerDetails) {
        addToCartParams = {
          ...addToCartParams,
          installerId: preSelectedInstaller,
          appointmentTime: undefined,
        };
      }
      setIsAddingFromCatalog(true);
      addToCart(addToCartParams);

      rudderstackSendTrackEvent(RudderstackTrackEventName.REORDER_PRODUCT, {
        brand: brand.label ?? '',
        category: category ?? '',
        coupon: '',
        currency: 'USD',
        imageUrl: image.src ?? '',
        line: line ?? '',
        price: price ?? '',
        productId,
        quantity: quantity ?? 0,
        sku: sku ?? '',
        url: steerUrl ?? '',
        variant: variant ?? '',
      });
    },
    [
      addToCart,
      cartSummary?.installerDetails,
      category,
      line,
      productId,
      sku,
      brand,
      image,
      price,
      steerUrl,
      variant,
    ],
  );

  useEffect(() => {
    if (isAddingFromCatalog && !isAddingToCart) {
      setIsAddingFromCatalog(false);
      setIsCartSummaryModalOpen(true);
      setIsLoading(false);
    }
  }, [isAddingFromCatalog, isAddingToCart, setIsCartSummaryModalOpen]);

  const handleCancelBtnClick = (event: MouseEvent) => {
    setActiveItemID(productId);
    event.preventDefault();
    const body = {
      reasonId: null,
      comment: '',
      quantity,
      attachedImages: [],
    };
    sendReturnRequest({
      body,
      orderId: String(orderUuid),
      productId: String(productId),
      type: 'cancel',
      zip: String(zip),
    } as ReturnRequestProps);
  };

  return (
    <div css={styles.container}>
      <OrderDetails
        productId={productId}
        brand={brand}
        canCustomerReorder={canCustomerReorder}
        canCustomerReturn={canCustomerReturn}
        isLoading={isLoading}
        category={category}
        image={image}
        line={line}
        name={name}
        poNumber={poNumber}
        price={price}
        sku={sku}
        steerUrl={steerUrl}
        quantity={quantity}
        returnQuantity={returnQuantity}
        resolveLoanId={resolveLoanId}
        variant={variant}
        handleAddToCart={handleAddToCart}
        handleReturnBtnClick={handleReturnBtnClick}
        isDealerTire={isDealerTire}
        isOTS={isOTS}
      />

      <div css={styles.buttonContainer}>
        {canCustomerReorder && !isTablet && (
          <Button
            css={styles.reorderButton}
            style={BUTTON_STYLE.OUTLINED}
            theme={THEME.LIGHT}
            onClick={handleAddToCart}
          >
            {isLoading && <Loading theme={THEME.LIGHT} />}
            <span>{ui('tracking.reorderOption')}</span>
          </Button>
        )}
        {!canCustomerReorder && !isTablet && (
          <span css={styles.reorderDisabled}>
            {ui('account.disabledReorderDescription')}
          </span>
        )}
      </div>
      <div css={styles.buttonContainer}>
        {!isTablet && !isDealerTire && !isOTS && (
          <Button
            css={styles.reorderButtonDisable}
            style={BUTTON_STYLE.OUTLINED}
            theme={THEME.LIGHT}
            onClick={handleReturnBtnClick}
            isDisabled={!canCustomerReturn}
          >
            {ui('tracking.returnOption')}
          </Button>
        )}
        {canCustomerReturn &&
          isLoadingReturnReasons &&
          activeItemID === productId && (
            <div css={styles.submitLoader}>
              <Loading />
            </div>
          )}
      </div>
      <div css={styles.buttonContainer}>
        {canCustomerCancelReturn && (
          <Button
            css={styles.reorderButton}
            style={BUTTON_STYLE.OUTLINED}
            theme={THEME.LIGHT}
            onClick={handleCancelBtnClick}
          >
            {ui('tracking.cancelOption')}
          </Button>
        )}

        {canCustomerCancelReturn &&
        (isSendingReturnOrCancelReq || returnOrCancelReqSent) &&
        activeItemID === productId ? (
          <div css={styles.submitLoader}>
            <Loading />
          </div>
        ) : null}
      </div>
      {!canCustomerReturn && !isTablet && !isDealerTire && !isOTS && (
        <span css={styles.reorderDisabled}>
          {ui('account.disabledReturnDescription')}
        </span>
      )}
    </div>
  );
}
export default OrderItem;
