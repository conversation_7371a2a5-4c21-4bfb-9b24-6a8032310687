import { GetServerSideProps } from 'next';
import dynamic from 'next/dynamic';
import nookies from 'nookies';

import {
  SiteCartBillingApiResponse,
  SiteCartBillingResponse,
} from '~/data/models/SiteCartBillingResponse';
import {
  SiteCartShippingApiResponse,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetCartSummary } from '~/lib/backend/cart-summary';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { AsyncResponse } from '~/lib/fetch/index.types';
import { getUserIp } from '~/lib/utils/ip';
import { getStringifiedParams } from '~/lib/utils/routes';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

const AffirmConfirmationContainer = dynamic(
  () =>
    import(
      '~/components/pages/CheckoutPage/AffirmConfirmation/AffirmConfirmation'
    ),
);

interface Props {
  cartSummary: SiteCartSummary | null;
  checkout_token: string;
  siteBilling: SiteCartBillingResponse | null;
  siteShipping: SiteCartShippingResponse | null;
  userIp: string | null;
}

function AffirmConfirmation(props: Props) {
  return <AffirmConfirmationContainer {...props} />;
}

export const getServerSideProps: GetServerSideProps<Props> = async (
  context,
) => {
  backendBootstrap({ request: context.req });
  const userIp = getUserIp(context.req);
  const { checkout_token } = getStringifiedParams(context.query);
  const cookies = nookies.get(context);
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;
  const headerCookie = context.req?.headers?.cookie ?? '';
  const userRegion = cookies[COOKIES.REGION];
  const userZip = cookies[COOKIES.ZIP];
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }

  const cartShippingResponse: AsyncResponse<SiteCartShippingApiResponse> =
    await backendGetCartShipping({ cartId: storedCartId });

  const cartBillingResponse: AsyncResponse<SiteCartBillingApiResponse> =
    await backendGetCartBilling({ cartId: storedCartId });

  const cartSummaryResponse: AsyncResponse<SiteCartSummaryResponse> =
    await backendGetCartSummary({
      id: storedCartId,
      query: {
        ssoUid: ssoUserId || '',
        userRegion,
        userZip,
      },
    });

  return {
    props: {
      cartSummary: cartSummaryResponse.isSuccess
        ? cartSummaryResponse.data.siteCart
        : null,
      checkout_token,
      siteBilling: cartBillingResponse.isSuccess
        ? cartBillingResponse.data.siteCartBillingResponse
        : null,
      siteShipping: cartShippingResponse.isSuccess
        ? cartShippingResponse.data.siteCartShippingResponse
        : null,
      userIp: userIp || null,
    },
  };
};
export default AffirmConfirmation;
