import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';

import ConfirmYourOrder, {
  ServerData,
} from '~/components/pages/CheckoutPage/ConfirmYourOrder/ConfirmYourOrder';
import { AppRouteCheckoutConfirmOrderPageParams } from '~/data/AppRoutePageParams';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendGetCartSummary } from '~/lib/backend/cart-summary';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { backendGetConfirmOrder } from '~/lib/backend/checkout/order-confirmation';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getStringifiedParams } from '~/lib/utils/routes';

async function CheckoutConfirmOrderPage(
  appRoutePageParams: AppRouteCheckoutConfirmOrderPageParams,
) {
  const { orderId } = await appRoutePageParams.params;
  const pageParams = await appRoutePageParams.searchParams;
  const query = getStringifiedParams(pageParams);

  if (!orderId) {
    notFound();
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const cartOrder = await backendGetConfirmOrder({
    extraQueryParams,
    query: { ...query, orderId },
  });

  if (!cartOrder.isSuccess) {
    const errorStatusCode = cartOrder.error?.statusCode || 500;
    if (errorStatusCode === 404) {
      notFound();
    }
    throw cartOrder.error;
  }

  if (!cartOrder.data) {
    notFound();
  }

  const cookieStore = await cookies();
  const ssoTokenInCookie = cookieStore.get(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
  )?.value;
  let ssoUserId: string | undefined;
  if (ssoTokenInCookie) {
    const response = await backendGetUserIdFromSSOToken(ssoTokenInCookie);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }
  const cartId = cartOrder.data.order.cartId ?? '';

  const [cartShippingResponse, cartBillingResponse, cartSummaryResponse] =
    await Promise.all([
      backendGetCartShipping({ cartId }),
      backendGetCartBilling({ cartId }),
      backendGetCartSummary({
        id: cartId,
        query: { ssoUid: ssoUserId || '', userRegion, userZip },
      }),
    ]);

  // Prepare the server data for the component
  const serverData: ServerData = {
    cartId,
    orderIdEncypt: orderId,
    siteBilling: cartBillingResponse.isSuccess
      ? cartBillingResponse.data.siteCartBillingResponse
      : null,
    siteCartOrder: cartOrder.data,
    siteCartSummary: cartSummaryResponse.isSuccess
      ? cartSummaryResponse.data
      : null,
    siteShipping: cartShippingResponse.isSuccess
      ? cartShippingResponse.data.siteCartShippingResponse
      : null,
  };

  return <ConfirmYourOrder {...serverData} />;
}

export default CheckoutConfirmOrderPage;
