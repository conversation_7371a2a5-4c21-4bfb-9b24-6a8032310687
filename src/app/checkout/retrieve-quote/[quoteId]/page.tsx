import { notFound } from 'next/navigation';

import setCartIdInCookie from '~/actions/checkout/retrieve-quote/cookie';
import RetrieveQuotePage from '~/components/pages/RetrieveQuote/RetrieveQuote';
import { AppRouteCheckoutRetrieveQuotePageParams } from '~/data/AppRoutePageParams';
import { backendUpdateSalesQuote } from '~/lib/backend/sales-quote';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';

async function CheckoutRetrieveQuotePage(
  appRoutePageParams: AppRouteCheckoutRetrieveQuotePageParams,
) {
  const { quoteId } = await appRoutePageParams.params;

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
  });

  const salesQuoteResp = await backendUpdateSalesQuote(
    {
      quoteId,
    },
    extraQueryParams,
  );

  if (!salesQuoteResp.isSuccess) {
    const errorStatusCode = salesQuoteResp.error.statusCode;
    if (errorStatusCode === 404) {
      notFound();
    } else {
      throw salesQuoteResp.error;
    }
  }

  if (!salesQuoteResp.data) {
    notFound();
  }

  const cartId = salesQuoteResp.data.cartId ?? '';

  return (
    <RetrieveQuotePage cartId={cartId} setCartIdInCookie={setCartIdInCookie} />
  );
}

export default CheckoutRetrieveQuotePage;
