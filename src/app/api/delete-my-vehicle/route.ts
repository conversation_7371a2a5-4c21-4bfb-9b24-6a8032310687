import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

import { backendDeleteMyVehicle } from '~/lib/backend/account/my-vehicle';
import { backendGetCustomerId } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import logger from '~/lib/helpers/logger';

export async function DELETE(request: NextRequest) {
  await backendBootstrap();

  const cookieStore = await cookies();
  const ssoToken = cookieStore.get(SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO)?.value;

  if (!ssoToken) {
    logger.error('No SSO token found in cookies');
    return new Response(null, { status: 401 });
  }

  const userId = await backendGetCustomerId(ssoToken);

  if (!userId) {
    logger.error('Failed to get customer ID from SSO token');
    return new Response(null, { status: 401 });
  }

  const searchParams = request.nextUrl.searchParams;
  const vehicleId = searchParams.get('vehicleId');

  if (!vehicleId) {
    logger.error('vehicleId parameter is required');
    return new Response(null, { status: 400 });
  }

  const params = {
    userId: String(userId),
    vehicleId: String(vehicleId),
  };

  const res = await backendDeleteMyVehicle(params);

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
