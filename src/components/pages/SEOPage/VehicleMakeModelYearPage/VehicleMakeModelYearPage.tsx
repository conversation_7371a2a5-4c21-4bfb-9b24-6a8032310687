'use client';

import isStrictEqual from 'fast-deep-equal';
import { useParams } from 'next/navigation';
import { memo, useCallback, useState } from 'react';

import BreadcrumbsComponent from '~/components/global/Breadcrumbs/Breadcrumbs';
import { BreadcrumbsItemProps } from '~/components/global/Breadcrumbs/BreadcrumbsItem.types';
import DataTableVertical from '~/components/global/DataTables/DataTableVertical';
import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import showStyle, {
  themeStyles,
} from '~/components/global/HeaderLandingPage/HeaderLandingPage.styles';
import HeaderWithLogo from '~/components/global/HeaderWithLogo/HeaderWithLogo';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Image from '~/components/global/Image/Image';
import Markdown from '~/components/global/Markdown/Markdown';
import ProductGroupList from '~/components/global/ProductGroupList/ProductGroupList';
import { ProductListingProps } from '~/components/global/ProductListing/ProductListing.types';
import { useSearchContextSelector } from '~/components/modules/Search/Search.context';
import {
  SearchStateEnum,
  SearchStateQueryType,
} from '~/components/modules/Search/Search.types';
import { useSearchModalContextSelector } from '~/components/modules/Search/SearchModal.context';
import { SiteAlternateYearModel } from '~/data/models/SiteAlternateYearModel';
import { SiteVehicleMakeModelYearResponse } from '~/data/models/SiteVehicleMakeModelYearResponse';
import { MARKDOWN_PRIMITIVES_WITH_HTML } from '~/lib/constants/markdown';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { THEME } from '~/lib/constants/theme';
import { mapArrayToBreadcrumbs } from '~/lib/utils/breadcrumbs';
import { capitalizeEveryWord } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';

import FaqSection from '../VehicleMakeModelListPage/FaqSection/FaqSection';
import Separator from '../VehicleMakeModelListPage/Separator/Separator';
import vmmStyles from '../VehicleMakeModelListPage/VehicleMakeModelListPage.styles';
import { mapAccordionToSiteModuleProductLineFAQs } from '../VehicleMakeModelListPage/VehicleMakeModelListPage.utils';
import AlternateYearModels from './AlternateYearModels';
import { data } from './AlternateYearModels.data';
import ReviewListGroup from './ReviewListGroup/ReviewListGroup';
import { mapModelListData } from './VehicleMakeModelYear.utils';
import styles from './VehicleMakeModelYearPage.styles';
import VehicleMakeModelYearPageDataStructure from './VehicleMakeModelYearPageDataStructure';

interface Props {
  pageData: SiteVehicleMakeModelYearResponse;
}

function VehicleMakeModelYearPage({ pageData }: Props) {
  const params = useParams();
  const { make, model } = params ?? {};

  const { lockSearchStateToYMM, setRouteQueryParamOptions, setFilterPills } =
    useSearchContextSelector((v) => ({
      lockSearchStateToYMM: v.lockSearchStateToYMM,
      setFilterPills: v.setFilterPills,
      setRouteQueryParamOptions: v.setRouteQueryParamOptions,
    }));
  const { setIsSearchOpen, setCurrentInputQuery } =
    useSearchModalContextSelector((v) => ({
      setIsSearchOpen: v.setIsSearchOpen,
      setCurrentInputQuery: v.setCurrentInputQuery,
    }));
  const {
    alternativeYearModels,
    year,
    header,
    body,
    trimTireSizeList,
    reviews,
    heroImage,
    faqItems,
    formatting,
    curatedProducts,
  } = pageData;

  const [mostPopularCurationProducts, bestRatedCurationProducts] =
    curatedProducts ?? [];

  const [showFullBody, setShowFullBody] = useState(false);
  const modelListTableData = trimTireSizeList
    ? mapModelListData(trimTireSizeList)
    : null;

  const alternateYearModelListTableData: Array<SiteAlternateYearModel> =
    alternativeYearModels ? alternativeYearModels : data;

  const makeName = capitalizeEveryWord(pageData.makeName);
  const makeModelName = capitalizeEveryWord(pageData.makeModelName);

  const pageBreadCrumbData: BreadcrumbsItemProps[] = mapArrayToBreadcrumbs([
    {
      type: ROUTES.HOME,
    },
    {
      type: ROUTES.VEHICLES,
    },
    {
      label: makeName,
      url: `/vehicles/${make}`,
    },
    {
      label: makeModelName,
      url: `/vehicles/${make}/${model}`,
    },
    {
      label: year,
      url: `/vehicles/${make}/${model}/${year}`,
    },
  ]);

  const toggleFullBody = useCallback(() => {
    setShowFullBody(!showFullBody);
  }, [showFullBody, setShowFullBody]);
  const splitBody = formatting
    ? formatting.split(/\n\n/g)
    : body && body.split(/\n\n/g);
  const briefBody = splitBody && splitBody[0];
  const moreBody =
    splitBody && splitBody.length > 1 && splitBody.slice(1).join('\n\n');

  const showMoreButton = briefBody && splitBody && splitBody.length > 1;
  const tireName = `${year} ${makeName} ${makeModelName}`;
  const faqProps =
    faqItems && mapAccordionToSiteModuleProductLineFAQs(faqItems);

  const handleConfirmSizeClick = useCallback(
    (product: ProductListingProps) => {
      const brand = product.brand.label;
      const productLine = product.productLine ?? '';
      const productLineId = product.productLineId
        ? `${product.productLineId}`
        : '';
      const makeModelYear = `${makeName} ${makeModelName} ${year}`;
      const query = {
        queryText: makeModelYear,
        queryType: SearchStateQueryType[SearchStateEnum.MAKE_MODEL_YEAR],
      };
      const params = {
        brand,
        productLineId,
      };
      setRouteQueryParamOptions({
        routes: [
          ROUTE_MAP[ROUTES.VEHICLE_CATALOG],
          ROUTE_MAP[ROUTES.TIRE_SIZE_CATALOG_OR_CATEGORY],
        ],
        params,
      });
      setFilterPills([
        {
          type: SearchStateEnum.BRAND_AND_PRODUCT_LINE,
          label: `${brand} ${productLine}`,
        },
      ]);
      setCurrentInputQuery(query);
      lockSearchStateToYMM(makeModelYear, brand, {
        pillName: `${brand} ${productLine}`,
        pillNumber: 1,
      });
      setIsSearchOpen(true);
    },
    [
      setRouteQueryParamOptions,
      setFilterPills,
      lockSearchStateToYMM,
      setIsSearchOpen,
      makeName,
      makeModelName,
      year,
      setCurrentInputQuery,
    ],
  );

  return (
    <div css={vmmStyles.root}>
      <VehicleMakeModelYearPageDataStructure
        make={make}
        model={model}
        year={year}
        bestRatedCurationProducts={bestRatedCurationProducts}
        makeModelName={makeModelName}
        makeName={makeName}
        mostPopularCurationProducts={mostPopularCurationProducts}
        tireName={tireName}
        trimTireSizeList={trimTireSizeList}
      />
      <Grid>
        <GridItem>
          <div css={vmmStyles.breadCrumbs}>
            <BreadcrumbsComponent
              navigationItems={pageBreadCrumbData}
              disableJsonLD
            />
          </div>
        </GridItem>
        {heroImage && (
          <GridItem fullbleed gridColumnXL={'4/12'}>
            <Image
              {...heroImage}
              customContainerStyles={styles.imageContainer}
              priority
              widths={[320, 768, 870]}
            />
          </GridItem>
        )}
        <GridItem
          gridColumn={'2/6'}
          gridColumnM={'2/8'}
          gridColumnL={'2/14'}
          gridColumnXL={'4/12'}
        >
          {header && (
            <div css={[vmmStyles.pageHeader]}>
              {pageData && (
                <HeaderWithLogo
                  {...header}
                  imageLabel={`${makeName}`}
                  body={undefined}
                  title={header.title + ' Tires'}
                />
              )}
            </div>
          )}
          {briefBody && (
            <div css={[showStyle.Body, vmmStyles.gray70]}>
              <Markdown
                allowedElements={MARKDOWN_PRIMITIVES_WITH_HTML}
                unwrapDisallowed
              >
                {briefBody}
              </Markdown>
            </div>
          )}
          {moreBody && (
            <div
              css={[showStyle.moreBody, vmmStyles.gray70]}
              aria-hidden={!showFullBody}
              id="vehicle-detail-page-body"
            >
              <Markdown unwrapDisallowed>{moreBody}</Markdown>
            </div>
          )}
          {showMoreButton && (
            <button
              aria-expanded={showFullBody}
              aria-labelledby="header-detail-page-body"
              aria-controls="header-detail-page-body"
              onClick={toggleFullBody}
              css={[
                showStyle.showFullBody,
                themeStyles[THEME.LIGHT].buttonHover,
              ]}
            >
              <span>{showFullBody ? 'Read less' : 'Read more'}</span>
              <Icon
                name={showFullBody ? ICONS.CHEVRON_UP : ICONS.CHEVRON_DOWN}
                css={showStyle.showFullBodyIcon}
              />
            </button>
          )}
        </GridItem>
      </Grid>
      <Separator customStyles={vmmStyles.mt30} />
      <Grid>
        <GridItem
          gridColumn={'2/6'}
          gridColumnM={'2/8'}
          gridColumnL={'2/14'}
          gridColumnXL={'4/12'}
        >
          <Grid css={vmmStyles.curations}>
            {!!mostPopularCurationProducts &&
              mostPopularCurationProducts?.productList?.length && (
                <GridItem fullbleed>
                  <ProductGroupList
                    name={mostPopularCurationProducts.name}
                    productList={mostPopularCurationProducts.productList}
                    id={'most-popular'}
                    icon={mostPopularCurationProducts.icon}
                    siteQueryParams={
                      mostPopularCurationProducts.siteQueryParams
                    }
                    description={mostPopularCurationProducts.description}
                    hideWarranty
                    onConfirmSize={handleConfirmSizeClick}
                    isFromYMMPage
                  />
                </GridItem>
              )}
          </Grid>
          <Grid css={vmmStyles.curations}>
            {!!bestRatedCurationProducts &&
              !!bestRatedCurationProducts?.productList?.length && (
                <GridItem fullbleed>
                  <ProductGroupList
                    name={bestRatedCurationProducts.name}
                    productList={bestRatedCurationProducts.productList}
                    id={'best-rated'}
                    icon={bestRatedCurationProducts.icon}
                    siteQueryParams={bestRatedCurationProducts.siteQueryParams}
                    description={bestRatedCurationProducts.description}
                    hideWarranty
                    onConfirmSize={handleConfirmSizeClick}
                    isFromYMMPage
                  />
                </GridItem>
              )}
          </Grid>
        </GridItem>
      </Grid>

      <Separator />
      {modelListTableData && (
        <Grid css={[vmmStyles.topSpacing, vmmStyles.bottomSpacing]}>
          <GridItem
            gridColumn={'2/6'}
            gridColumnM={'2/8'}
            gridColumnL={'2/14'}
            gridColumnXL={'4/12'}
          >
            <h2 css={styles.dataTableHeader}>
              {ui('seoPage.vehicleYMMPage.table.title', {
                tireName,
              })}
            </h2>
            <DataTableVertical
              {...modelListTableData}
              isFirstColumnFixed
              isSSR
              customTableStyles={vmmStyles.dataTableWrapper}
              customContainerStyles={vmmStyles.dataTableContainer}
            />
          </GridItem>
        </Grid>
      )}

      {reviews && !!reviews.reviewsListing.length && (
        <div>
          <ReviewListGroup {...reviews} />
          <Separator customStyles={vmmStyles.topSpacing} />
        </div>
      )}
      {faqProps && (
        <div>
          <FaqSection {...faqProps} />
          <Separator />
        </div>
      )}
      {data && <AlternateYearModels data={alternateYearModelListTableData} />}
    </div>
  );
}

export default memo(VehicleMakeModelYearPage, (prevProps, nextProps) =>
  isStrictEqual(prevProps, nextProps),
);
