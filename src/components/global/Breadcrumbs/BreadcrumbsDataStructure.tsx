'use client';

import isStrictEqual from 'fast-deep-equal';
import { memo } from 'react';
import { BreadcrumbList } from 'schema-dts';

import DataStructure from '~/components/global/DataStructure/DataStructure';
import useHostUrl from '~/hooks/useHostUrl';

import { BreadcrumbsItemProps } from './BreadcrumbsItem.types';

interface BreadcrumbsDataStructureProps {
  navigationItems: BreadcrumbsItemProps[];
}

function BreadcrumbsDataStructure({
  navigationItems,
}: BreadcrumbsDataStructureProps) {
  const hostUrl = useHostUrl();

  const dataStructure: BreadcrumbList | null = hostUrl
    ? {
        '@type': 'BreadcrumbList',
        itemListElement: navigationItems.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@id': `${hostUrl}${item.url}`,
            name: item.label,
          },
        })),
      }
    : null;

  return dataStructure ? (
    <DataStructure id="breadcrumb" jsonLD={dataStructure} />
  ) : null;
}

export default memo(BreadcrumbsDataStructure, (prevPros, nextProps) =>
  isStrictEqual(prevPros, nextProps),
);
