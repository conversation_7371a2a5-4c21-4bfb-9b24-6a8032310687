import { cookies, headers } from 'next/headers';
import { notFound } from 'next/navigation';

import AccountContainer from '~/components/modules/Account/Account.container';
import AccountSimple360Container from '~/components/modules/Account/AccountSimple360.container';
import { AppRouteAccountPageParams } from '~/data/AppRoutePageParams';
import { backendGetSSOToken } from '~/lib/backend/account/sso-token';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { SSO_COOKIE_CONSTANTS, STATE_CONSTANTS } from '~/lib/constants/sso';
import {
  AccountDetails,
  SSOTokenInput,
  SSOTokenResponse,
} from '~/lib/constants/sso.types';
import {
  isLocal as isLocalDeployment,
  isProductionDeploy as isProductionDeployment,
} from '~/lib/utils/deploy';

const isLocal = isLocalDeployment();
const isProductionDeploy = isProductionDeployment();
const clientId = 'steer';
const clientSecret =
  (isProductionDeploy
    ? process.env.STEER_CLIENT_SECERET_SSO
    : process.env.STEER_CLIENT_SECERET_SSO_INTEGRATION) ?? '';
const grantType = 'authorization_code';
const defaultUserDetails = {
  accountTypes: [],
  applications: [],
  email: '',
  hasToken: false,
  userFirstName: 'FirstName',
  username: 'User',
};

export default async function AccountPage(
  appRoutePageParams: AppRouteAccountPageParams,
) {
  const { code, state } = await appRoutePageParams.searchParams;
  const cookieStore = await cookies();
  const headersList = await headers();

  const ssoTokenInCookie = cookieStore.get(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
  )?.value;
  const csrfTokenInCookie = cookieStore.get(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_CSRF,
  )?.value;
  const redirectUri =
    cookieStore.get(SSO_COOKIE_CONSTANTS.ACCOUNT_REDIRECT)?.value ?? '';
  const host = headersList.get('host');
  const simple360RedirectUri = `${
    isLocal ? 'http' : 'https'
  }://${host}/account`;

  const body: SSOTokenInput = {
    client_id: clientId,
    client_secret: clientSecret,
    code: code ?? '',
    grant_type: grantType,
    redirect_uri:
      state === STATE_CONSTANTS.SIMPLE360 ? simple360RedirectUri : redirectUri,
  };

  let userDetails: AccountDetails = defaultUserDetails;

  if (
    !ssoTokenInCookie &&
    ((csrfTokenInCookie !== undefined && csrfTokenInCookie === state) ||
      state === STATE_CONSTANTS.SIMPLE360)
  ) {
    const res = await backendGetSSOToken(body);
    if (res.isSuccess && res.data) {
      const data = res.data as unknown as SSOTokenResponse;
      if (data.access_token) {
        const response = await backendGetUserIdFromSSOToken(data.access_token);
        if (response.isSuccess) {
          const userFirstName = response.data.firstName
            ? `${response.data.firstName}`
            : 'UserFirstName';
          const username = response.data.firstName
            ? `${response.data.firstName} ${response.data.lastName}`
            : 'User';
          userDetails = {
            accountTypes: response.data.accountTypes,
            applications: response.data.applications || [],
            email: response.data?.username,
            hasToken: true,
            userFirstName,
            username,
          };

          const setCookie = async () => {
            'use server';

            // Set the SSO token cookie using the cookies() API
            cookieStore.set(
              SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
              data.access_token,
              {
                domain: isLocal ? undefined : SSO_COOKIE_CONSTANTS.DOMAIN,
                maxAge: 86400 * 30,
                path: '/',
                secure: !isLocal,
              },
            );
          };

          return (
            <AccountSimple360Container setCookie={setCookie} {...userDetails} />
          );
        }
      } else {
        notFound();
      }
    }
  } else if (!ssoTokenInCookie && !code && !state) {
    // No SSO token and no auth flow in progress
    return <AccountContainer {...userDetails} />;
  } else if (ssoTokenInCookie) {
    // User has existing SSO token
    const response = await backendGetUserIdFromSSOToken(ssoTokenInCookie);
    if (response.isSuccess) {
      const userFirstName = response.data.firstName
        ? `${response.data.firstName}`
        : 'UserFirstName';
      const username = response.data.firstName
        ? `${response.data.firstName} ${response.data.lastName}`
        : 'User';
      userDetails = {
        accountTypes: response.data.accountTypes,
        applications: response.data.applications || [],
        email: response.data?.username,
        hasToken: true,
        userFirstName,
        username,
      };
    }
  }

  return <AccountContainer {...userDetails} />;
}
