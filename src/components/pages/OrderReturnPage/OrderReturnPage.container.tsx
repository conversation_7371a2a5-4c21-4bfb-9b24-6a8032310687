'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense, useEffect } from 'react';

import OrderLoading from '~/components/pages/OrderTrackingResult/OrderLoading/OrderLoading';
import { useOrderTrackingResultContextSelector } from '~/components/pages/OrderTrackingResult/OrderTrackingResult.context';
import { useSiteGlobalsContextSelector } from '~/context/SiteGlobals.context';
import { OrderProduct } from '~/data/models/OrderProduct';
import { OrderTrackingInput } from '~/data/models/OrderTrackingInput';
import useRouter from '~/hooks/useRouter';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { hex2a } from '~/lib/utils/string';

import OrderReturnPage from './OrderReturnPage';

type OrderItemProps = OrderProduct & OrderTrackingInput;

function OrderReturnPageContainer() {
  const { customerServiceEnabled, customerServiceNumber } =
    useSiteGlobalsContextSelector((v) => ({
      customerServiceEnabled: v.customerServiceEnabled,
      customerServiceNumber: v.customerServiceNumber,
    }));

  const router = useRouter();
  const searchParams = useSearchParams();

  let orderId =
    searchParams?.get('orderId') ?? searchParams?.get('track_order');
  let zip = searchParams?.get('zip') ?? searchParams?.get('track_shipping_zip');
  let id = searchParams?.get('id') ?? searchParams?.get('track_order');

  if (orderId && zip && zip.length > 5) {
    orderId = hex2a(orderId.toString());
    zip = hex2a(zip.toString());
    id = hex2a(orderId.toString());
  }

  const {
    getOrderTracking,
    hasError,
    isLoadingOrder,
    order,
    isLoadingReturnReasons,
    returnTireData,
    returnReasons,
    errorInReturnReasons,
    getReturnReasons,
    sendReturnRequest,
    isSendingReturnOrCancelReq,
    returnOrCancelReqError,
    returnOrCancelReqSent,
  } = useOrderTrackingResultContextSelector((v) => ({
    errorInReturnReasons: v.errorInReturnReasons,
    getOrderTracking: v.getOrderTracking,
    getReturnReasons: v.getReturnReasons,
    hasError: v.hasError,
    isLoadingOrder: v.isLoadingOrder,
    isLoadingReturnReasons: v.isLoadingReturnReasons,
    isSendingReturnOrCancelReq: v.isSendingReturnOrCancelReq,
    order: v.order,
    returnOrCancelReqError: v.returnOrCancelReqError,
    returnOrCancelReqSent: v.returnOrCancelReqSent,
    returnReasons: v.returnReasons,
    returnTireData: v.returnTireData,
    sendReturnRequest: v.sendReturnRequest,
  }));

  useEffect(() => {
    if (returnOrCancelReqSent) {
      window.scrollTo(0, 0);
      setTimeout(() => {
        router.push(
          `${ROUTE_MAP[ROUTES.ORDER_TRACKING_RESULT]}?orderId=${orderId}&zip=${zip}`,
        );
      }, 500);
    }
  }, [isSendingReturnOrCancelReq, orderId, returnOrCancelReqSent, router, zip]);

  useEffect(() => {
    if (!returnTireData && !isLoadingReturnReasons && order) {
      // checking if the product ID exist in the URL
      const { orderProducts } = order;
      const orderInQuery = orderProducts.filter(
        (item: OrderProduct) => item.productId.toString() === id,
      );
      if (orderInQuery && orderInQuery.length > 0) {
        // checking if the user can return/cancel return
        const selectedProduct = orderInQuery[0];
        const { canCustomerCancelReturn, canCustomerReturn } = selectedProduct;
        if (canCustomerCancelReturn || canCustomerReturn) {
          getReturnReasons({
            ...selectedProduct,
            orderId: String(order.id),
            zip: String(zip),
          } as OrderItemProps);
        } else {
          // if cannot return/cancel return redirect back to order tracking result
          router.push(
            `${ROUTE_MAP[ROUTES.ORDER_TRACKING_RESULT]}?orderId=${orderId}&zip=${zip}`,
          );
        }
      } else if (orderId && zip) {
        router.push(
          `${ROUTE_MAP[ROUTES.ORDER_TRACKING_RESULT]}?orderId=${orderId}&zip=${zip}`,
        );
      } else {
        router.push(ROUTE_MAP[ROUTES.ORDER_TRACKING]);
      }
    }
  }, [
    isLoadingReturnReasons,
    returnTireData,
    getReturnReasons,
    order,
    orderId,
    zip,
    id,
    router,
  ]);

  useEffect(() => {
    if (errorInReturnReasons) {
      router.push(ROUTE_MAP[ROUTES.ORDER_TRACKING]);
    }
  }, [errorInReturnReasons, router]);

  useEffect(() => {
    if (!order && !isLoadingOrder && !hasError) {
      getOrderTracking({ orderId, zip } as OrderTrackingInput);
    }
  }, [order, isLoadingOrder, hasError, orderId, zip, getOrderTracking]);

  useEffect(() => {
    if (hasError) {
      router.push(ROUTE_MAP[ROUTES.ORDER_TRACKING]);
    }
  }, [hasError, router]);

  if (isLoadingOrder || !order || isLoadingReturnReasons || !returnTireData) {
    return <OrderLoading />;
  }

  return (
    returnTireData && (
      <OrderReturnPage
        {...returnTireData}
        orderId={order.id}
        orderUuid={order.orderUuid}
        orderStatus={order.status}
        returnReasons={returnReasons}
        zip={zip}
        isSendingReturnOrCancelReq={isSendingReturnOrCancelReq}
        returnOrCancelReqError={returnOrCancelReqError}
        sendReturnRequest={sendReturnRequest}
        isCustomerServiceEnabled={customerServiceEnabled}
        customerServiceNumber={customerServiceNumber}
      />
    )
  );
}

function OrderReturnPageWrapper() {
  return (
    <Suspense fallback={null}>
      <OrderReturnPageContainer />
    </Suspense>
  );
}

export default OrderReturnPageWrapper;
