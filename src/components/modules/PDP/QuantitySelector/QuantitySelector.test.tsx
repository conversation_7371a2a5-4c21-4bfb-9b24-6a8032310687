import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import ReactModal from 'react-modal';

import { GlobalsContextProvider } from '~/context/Globals.context';
import { ui } from '~/lib/utils/ui-dictionary';

import QuantitySelectorContainer from './QuantitySelector.container';

ReactModal.setAppElement('*');

// Mock icons since jest-next-dynamic does not support svgx
jest.mock('~/components/global/Icon/Icon', () => () => 'Icon');
jest.mock('~/context/ComponentErrorBoundaryCounter.context', () => ({
  useComponentErrorBoundaryCounterContextSelector: () => ({
    increaseCount: jest.fn().mockReturnValue(true),
  }),
}));

const toggleModal = jest.fn();
const changeQuantity = jest.fn();
const defaultFrontOnlyQuantity = {
  front: 4,
  rear: 0,
};
const defaultFrontAndRearQuantity = {
  front: 2,
  rear: 2,
};

describe('modules/PDP/QuantitySelector', () => {
  afterEach(() => {
    changeQuantity.mockClear();
  });

  it(
    'confirms default quantity',
    async () => {
      render(
        <GlobalsContextProvider value={{}}>
          <QuantitySelectorContainer
            isOpen
            onChangeQuantity={changeQuantity}
            toggleModal={toggleModal}
            tirePrice="5000"
            initialQuantity={defaultFrontOnlyQuantity}
            maxQuantityAllowedForCart={16}
            minQuantityAllowedForCart={12}
          />
        </GlobalsContextProvider>,
      );

      const confirmButton = screen.getByTestId('confirm-button');

      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(changeQuantity).toHaveBeenCalledWith(defaultFrontOnlyQuantity);
      });
    },
    15 * 1000,
  );

  it('changes quantity and confirm', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          initialQuantity={defaultFrontOnlyQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const confirmButton = screen.getByTestId('confirm-button');

    const buttons = screen.getByTestId('front-picker');
    const buttonFive = within(buttons).getByText('+');

    fireEvent.click(buttonFive);
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(changeQuantity).toHaveBeenCalledWith({ front: 5, rear: 0 });
    });
  });

  it('intercepts 1 unit and confirms', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          initialQuantity={defaultFrontOnlyQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const confirmButton = screen.getByTestId('confirm-button');

    const buttons = screen.getByTestId('front-picker');
    const buttonOne = within(buttons).getByText('-');

    fireEvent.click(buttonOne);
    fireEvent.click(buttonOne);
    fireEvent.click(buttonOne);
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(changeQuantity).toHaveBeenCalledWith({ front: 4, rear: 0 });
    });
  });

  it('intercepts 1 unit and accept suggestion', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          initialQuantity={defaultFrontOnlyQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const confirmButton = screen.getByTestId('confirm-button');

    const buttons = screen.getByTestId('front-picker');
    const buttonOne = within(buttons).getByText('-');

    fireEvent.click(buttonOne);
    fireEvent.click(buttonOne);
    fireEvent.click(buttonOne);
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(changeQuantity).toHaveBeenCalledWith({ front: 4, rear: 0 });
    });
  });

  it('confirms front and rear default quantities', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isFrontAndRear
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          rearPrice="6000"
          initialQuantity={defaultFrontAndRearQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const confirmButton = screen.getByTestId('confirm-button');

    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(changeQuantity).toHaveBeenCalledWith(defaultFrontAndRearQuantity);
    });
  });

  it('changes front and rear quantities and confirm', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isFrontAndRear
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          rearPrice="6999"
          initialQuantity={defaultFrontAndRearQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const confirmButton = screen.getByTestId('confirm-button');

    const buttonsFront = screen.getByTestId('front-picker');
    const buttonFrontOne = within(buttonsFront).getByText('-');

    const buttonsRear = screen.getByTestId('rear-picker');
    const buttonRearThree = within(buttonsRear).getByText('+');

    fireEvent.click(buttonFrontOne);
    fireEvent.click(buttonRearThree);
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(changeQuantity).toHaveBeenCalledWith({ front: 2, rear: 3 });
    });
  });

  it('calculates front only total', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          initialQuantity={defaultFrontOnlyQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const price = screen.getByTestId('total-price');
    const buttons = screen.getByTestId('front-picker');
    const buttonOne = within(buttons).getByText('-');
    const buttonFive = within(buttons).getByText('+');

    fireEvent.click(buttonOne);

    await waitFor(() => {
      expect(price).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $200.00`,
      );
    });

    fireEvent.click(buttonFive);

    await waitFor(() => {
      expect(price).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $250.00`,
      );
    });
  });

  it('calculates front and rear total', async () => {
    render(
      <GlobalsContextProvider value={{}}>
        <QuantitySelectorContainer
          isFrontAndRear
          isOpen
          onChangeQuantity={changeQuantity}
          toggleModal={toggleModal}
          tirePrice="5000"
          rearPrice="6000"
          initialQuantity={defaultFrontOnlyQuantity}
          maxQuantityAllowedForCart={16}
          minQuantityAllowedForCart={12}
        />
      </GlobalsContextProvider>,
    );

    const prices = screen.getAllByTestId('total-price');

    const buttonsFront = screen.getByTestId('front-picker');
    const buttonFrontOne = within(buttonsFront).getByText('+');
    const buttonFrontTwo = within(buttonsFront).getByText('-');

    const buttonsRear = screen.getByTestId('rear-picker');
    const buttonRearOne = within(buttonsRear).getByText('+');
    const buttonRearTwo = within(buttonsRear).getByText('-');

    fireEvent.click(buttonFrontOne);
    fireEvent.click(buttonRearOne);

    await waitFor(() => {
      expect(prices[0]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $250.00`,
      );
    });
    await waitFor(() => {
      expect(prices[1]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $120.00`,
      );
    });

    fireEvent.click(buttonFrontTwo);

    await waitFor(() => {
      expect(prices[0]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $250.00`,
      );
    });
    await waitFor(() => {
      expect(prices[1]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $120.00`,
      );
    });

    fireEvent.click(buttonRearTwo);

    await waitFor(() => {
      expect(prices[0]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $250.00`,
      );
    });

    await waitFor(() => {
      expect(prices[1]).toHaveTextContent(
        `${ui('pdp.quantitySelector.totalPrice')} $120.00`,
      );
    });
  });
});
