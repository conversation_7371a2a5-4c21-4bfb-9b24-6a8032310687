import { GetServerSideProps } from 'next';
import nookies from 'nookies';

import OrderConfirmationContainer, {
  ServerData,
} from '~/components/pages/CheckoutPage/OrderConfirmation/OrderConfirmationContainer';
import { SiteCartOrderResponse } from '~/data/models/SiteCartOrderResponse';
import WithErrorPageHandling, {
  PageResponse,
} from '~/hocs/WithPageErrorHandling';
import { backendGetSSOTokenLegacy } from '~/lib/backend/account/sso-token';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetCartSummary } from '~/lib/backend/cart-summary';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetOrderLegacy } from '~/lib/backend/checkout/cart-order';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import {
  SSOTokenInput,
  SSOTokenResponse,
  SSOUserIdResponse,
} from '~/lib/constants/sso.types';
import { AsyncResponse } from '~/lib/fetch/index.types';
import { isProductionDeploy } from '~/lib/utils/deploy';
import { getStringifiedParams } from '~/lib/utils/routes';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

function OrderConfirmation(props: ServerData) {
  return <OrderConfirmationContainer {...props} />;
}

export const getServerSideProps: GetServerSideProps<
  PageResponse<ServerData>
> = async (context) => {
  backendBootstrap({ request: context.req });
  const cookies = nookies.get(context);
  const queryParams = getStringifiedParams(context.query);
  const { orderId } = queryParams;
  const storedOrderId = orderId ?? cookies[COOKIES.ORDER_ID] ?? null;
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;
  const userRegion = cookies[COOKIES.REGION];
  const userZip = cookies[COOKIES.ZIP];

  if (!storedCartId && !storedOrderId) {
    return { props: { errorStatusCode: 400 } };
  }

  const headerCookie = context.req?.headers?.cookie ?? '';
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }

  const cartOrder: AsyncResponse<SiteCartOrderResponse> =
    await backendGetOrderLegacy(
      {
        orderId: storedOrderId,
      },
      context.req,
    );

  const cartShippingResponse = await backendGetCartShipping(
    { cartId: storedCartId },
    context.req,
  );

  const cartBillingResponse = await backendGetCartBilling(
    { cartId: storedCartId },
    context.req,
  );

  const cartSummaryResponse = await backendGetCartSummary(
    {
      id: storedCartId,
      query: { ssoUid: ssoUserId || '', userRegion, userZip },
    },
    context.req,
  );

  const { code, state } = getStringifiedParams(context.query);
  const ssoTokenInCookie = cookies[SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO];
  const csrfTokenInCookie = cookies[SSO_COOKIE_CONSTANTS.SIMPLETIRE_CSRF];
  const redirectUri = cookies[SSO_COOKIE_CONSTANTS.ACCOUNT_REDIRECT];
  const clientSecret = isProductionDeploy()
    ? process.env.STEER_CLIENT_SECERET_SSO
    : process.env.STEER_CLIENT_SECERET_SSO_INTEGRATION;

  const body = {
    client_id: 'steer',
    client_secret: clientSecret,
    code,
    grant_type: 'authorization_code',
    redirect_uri: redirectUri,
  } as SSOTokenInput;

  let userDetails: SSOUserIdResponse | null = null;

  if (
    !ssoTokenInCookie &&
    csrfTokenInCookie !== undefined &&
    csrfTokenInCookie === state
  ) {
    const res = await backendGetSSOTokenLegacy(body);
    if (res.isSuccess && res.data) {
      const data = res.data as unknown as SSOTokenResponse;
      // we are saving two cookies after recieving the sso token
      // the first is saved without domain for local/vercel builds
      if (data.access_token) {
        nookies.set(
          context,
          SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
          data.access_token,
          {
            maxAge: 86400 * 30,
            path: '/',
          },
        );
        // the second one is saved with .simpletire.com domain for it to be accessible by checkout
        nookies.set(
          context,
          SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
          data.access_token,
          {
            maxAge: 86400 * 30,
            path: '/',
            domain: SSO_COOKIE_CONSTANTS.DOMAIN,
          },
        );
        const res = await backendGetUserIdFromSSOToken(data.access_token);
        if (res.isSuccess) {
          userDetails = res.data;
        }
      }
    } else if (ssoTokenInCookie) {
      const res = await backendGetUserIdFromSSOToken(ssoTokenInCookie);
      if (res.isSuccess) {
        userDetails = res.data;
      }
    }
  } else {
    const res = await backendGetUserIdFromSSOToken(ssoTokenInCookie);
    if (res.isSuccess) {
      userDetails = res.data;
    }
  }

  return {
    props: {
      cartId: storedCartId,
      orderId: storedOrderId,
      siteBilling: cartBillingResponse.isSuccess
        ? cartBillingResponse.data.siteCartBillingResponse
        : null,
      siteCartOrder: cartOrder.isSuccess ? cartOrder.data : null,
      siteCartSummary: cartSummaryResponse,
      siteShipping: cartShippingResponse.isSuccess
        ? cartShippingResponse.data.siteCartShippingResponse
        : null,
      userDetails,
    },
  };
};

export default WithErrorPageHandling(OrderConfirmation);
