import { OrderConfirmationRequest } from '~/data/models/OrderConfirmationRequest';
import { SiteCartOrderResponse } from '~/data/models/SiteCartOrderResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendConfirmOrder(
  input: OrderConfirmationRequest & { sessionId: string },
  userTime: string,
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartOrderResponse,
    OrderConfirmationRequest
  >({
    endpoint: '/v2/site/order-confirmation',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
    query: {
      userTime,
    },
  });
}

export async function backendGetConfirmOrder({
  extraQueryParams,
  query,
}: {
  extraQueryParams?: Record<string, string>;
  query?: Record<string, string>;
}) {
  return await fetchWithErrorHandling<SiteCartOrderResponse>({
    endpoint: '/v2/site/order-confirmation',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    query,
  });
}
