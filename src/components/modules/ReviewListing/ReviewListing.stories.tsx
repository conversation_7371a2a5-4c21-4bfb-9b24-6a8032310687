import { filterSortMock } from '~/components/modules/Catalog/Filters/Filters.mock';
import Header from '~/components/modules/ReviewListing/Header/Header';
import RatingsTable from '~/components/modules/ReviewListing/RatingsTable/RatingsTable';
import { GlobalsContextProvider } from '~/context/Globals.context';

import { reviewListingMock } from './ReviewListing.mock';

export default {
  component: RatingsTable,
  title: 'SEO Landing/Review Listing/Review Listing',
};

export function FullPage() {
  const { breadcrumbs, body, filters, title, ratings, listResultMetadata } =
    reviewListingMock;

  return (
    <GlobalsContextProvider value={{}}>
      <div>
        <Header
          breadcrumbs={breadcrumbs}
          body={body}
          title={title}
          filters={filters}
          selectedStars={[]}
          handleClickStar={() => {}}
          reviews={[]}
        />
        <RatingsTable
          reviews={ratings}
          listResultMetadata={listResultMetadata}
          sortList={filterSortMock}
          currentPage={1}
        />
      </div>
    </GlobalsContextProvider>
  );
}
