'use client';

import lscache from 'lscache';
import { setCookie } from 'nookies';

import useEffectOnlyOnce from '~/hooks/useEffectOnlyOnce';
import useRouter from '~/hooks/useRouter';
import { apiUpdateEmailQuote } from '~/lib/api/sales-quote';
import { COOKIES } from '~/lib/constants/cookies';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { TIME } from '~/lib/constants/time';
import debounce from '~/lib/utils/debounce';

interface Props {
  cartId: string;
  setCartIdInCookie: (cartId: string) => Promise<void>;
}

function RetrieveQuotePage({ cartId, setCartIdInCookie }: Props) {
  const router = useRouter();

  useEffectOnlyOnce(
    () => {
      lscache.set(LOCAL_STORAGE[PROPERTIES.RETRIEVE_QUOTE], 'yes');
      const isSessionPresent = lscache.get(LOCAL_STORAGE[PROPERTIES.SESSION]);
      async function updateUserSession() {
        await apiUpdateEmailQuote({
          input: {
            cartId,
            customerSessionId: isSessionPresent,
          },
          includeUserRegion: true,
          includeUserZip: true,
        });
      }
      const debouncedUpdateUserSession = debounce(
        () => {
          updateUserSession();
        },
        TIME.MS7000,
        { isImmediate: false },
      );

      const setCookieAndRedirect = async () => {
        await setCartIdInCookie(cartId);
        if (cartId && cartId !== undefined) {
          debouncedUpdateUserSession();
          const params = {
            maxAge: 86400 * 30, // valid for 1 month
            path: '/',
            secure: false,
            domain: COOKIES.DOMAIN,
          };
          setCookie(null, COOKIES.CART_ID, cartId.toString(), params);
          setCookie(null, COOKIES.CART_SHIPPING, '1', params);
          setCookie(null, COOKIES.CART_APPOINTMENT, '1', params);
          router.push(ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]);
        } else {
          router.push(ROUTE_MAP[ROUTES.HOME]);
        }
      };

      setCookieAndRedirect();
    },
    {},
    () => true,
  );

  return (
    <div>
      <div id="retrive-quote"></div>
    </div>
  );
}

export default RetrieveQuotePage;
