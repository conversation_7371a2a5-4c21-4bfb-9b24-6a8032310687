import { UserData } from '~/data/models/UserData';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import {
  ServiceUserIdInput,
  SSOUserIdResponse,
} from '~/lib/constants/sso.types';
import { fetchFromSSO, fetchWithErrorHandling } from '~/lib/fetch-backend';

export interface UserIdType {
  userId: string;
}

export async function extractTokenFromCookie(cookieHeader: string) {
  const cookie = SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO;
  let cookie_value;
  const i = cookieHeader.indexOf(cookie + '=');
  if (i !== -1) {
    const eq = i + cookie.length + 1;
    const end = cookieHeader.indexOf(';', eq);
    cookie_value = cookieHeader.substring(eq, end === -1 ? undefined : end);
  }
  return cookie_value;
}

export async function backendGetUserIdFromSSOToken(token: string) {
  return fetchFromSSO<SSOUserIdResponse>({
    endpoint: '/api/me',
    method: 'get',
    includeAuthorization: true,
    ssoToken: token,
  });
}

export async function backendGetCustomerId(token: string) {
  const response = await backendGetUserIdFromSSOToken(token);

  if (response.isSuccess) {
    const { uid, username, firstName, lastName, accountTypes } = response.data;
    let body: ServiceUserIdInput = {
      ssoUid: uid,
      email: username,
      ssoAccountType: accountTypes?.[0]?.name ?? '',
    };
    if (firstName && lastName) {
      body = { ...body, firstName, lastName };
    }

    const responseFromService = await fetchWithErrorHandling({
      endpoint: '/v2/customers',
      includeAuthorization: true,
      jsonBody: body,
      method: 'post',
    });

    if (responseFromService.isSuccess) {
      const userData = responseFromService.data as UserData;
      return userData.id;
    }
  }

  return null;
}
