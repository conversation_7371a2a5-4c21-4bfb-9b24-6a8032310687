import { headers } from 'next/headers';
import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendSendQuote,
  backendUpdateSalesQuote,
} from '~/lib/backend/sales-quote';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import logger from '~/lib/helpers/logger';
import { getUserSessionId } from '~/lib/utils/api';

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const searchParamsObject = Object.fromEntries(searchParams);

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query: searchParamsObject,
  });

  const headersList = await headers();
  const userSessionId = getUserSessionId(headersList);

  if (!userSessionId) {
    logger.error('Invalid authentication');
    return new Response(null, { status: 204 });
  }

  try {
    const body = await request.json();

    // Send the quote directly without checking
    const res = await backendSendQuote(
      {
        sessionId: userSessionId,
        ...body,
      },
      extraQueryParams,
    );

    if (res.isSuccess) {
      return new Response(JSON.stringify(res.data), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(null, {
      status: res.error.statusCode,
    });
  } catch (error) {
    logger.error('Error sending quote:', error);
    return new Response(
      JSON.stringify({
        Message: 'An error occurred while sending the quote.',
        Success: false,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const searchParamsObject = Object.fromEntries(searchParams);

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query: searchParamsObject,
  });

  try {
    const body = await request.json();

    const res = await backendUpdateSalesQuote(body, extraQueryParams);

    if (res.isSuccess) {
      return new Response(JSON.stringify(res.data), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(null, {
      status: res.error.statusCode,
    });
  } catch (error) {
    logger.error('Error updating sales quote:', error);
    return new Response(null, { status: 500 });
  }
}
