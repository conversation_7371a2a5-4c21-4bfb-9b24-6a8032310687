import DataStructure from '~/components/global/DataStructure/DataStructure';
import { SiteCatalogProductGroupItem } from '~/data/models/SiteCatalogProductGroupList';
import { SiteVehicleMakeModelList } from '~/data/models/SiteVehicleMakeModelList';
import useHostUrl from '~/hooks/useHostUrl';

import { generateYMMSchema } from './VehicleMakeModelYear.utils';

function VehicleMakeModelYearPageDataStructure({
  make,
  model,
  bestRatedCurationProducts,
  makeModelName,
  makeName,
  mostPopularCurationProducts,
  tireName,
  trimTireSizeList,
  year,
}: {
  bestRatedCurationProducts: SiteCatalogProductGroupItem;
  make: string | string[];
  makeModelName: string;
  makeName: string;
  model: string | string[];
  mostPopularCurationProducts: SiteCatalogProductGroupItem;
  tireName: string;
  trimTireSizeList: Array<SiteVehicleMakeModelList> | null;
  year: string;
}) {
  const hostUrl = useHostUrl();

  const dataStructure =
    hostUrl &&
    make &&
    model &&
    typeof make === 'string' &&
    typeof model === 'string'
      ? generateYMMSchema({
          bestRatedCurationProducts,
          hostUrl,
          make,
          makeModelName,
          makeName,
          model,
          mostPopularCurationProducts,
          tireName,
          trimTireSizeList,
          year,
        })
      : null;

  return dataStructure && <DataStructure id="ymm" jsonLD={dataStructure} />;
}

export default VehicleMakeModelYearPageDataStructure;
