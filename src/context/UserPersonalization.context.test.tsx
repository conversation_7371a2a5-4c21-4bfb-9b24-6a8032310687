import { renderHook, waitFor } from '@testing-library/react';

import * as BootstrapUtils from '~/lib/api/bootstrap';
import * as GetUserDetails from '~/lib/api/get-user-details';
import * as FetchUtils from '~/lib/fetch/utils';
import { UserPersonalization } from '~/data/models/UserPersonalization';

import { GlobalsContextProvider } from './Globals.context';
import AllThirdPartyScriptsContext from './ThirdPartyScriptsContext/AllThirdPartyScriptsContext';
import {
  defaultUserPersonalizationData,
  useContextSetup,
} from './UserPersonalization.context';

jest.mock('~/lib/api/bootstrap');
jest.mock('~/lib/fetch/utils');
jest.mock('~/lib/api/get-user-details');
const mockedBootstrapUtils = BootstrapUtils as jest.Mocked<
  typeof BootstrapUtils
>;
const mockedFetchUtils = FetchUtils as jest.Mocked<typeof FetchUtils>;
const mockGetUserDetails = GetUserDetails as jest.Mocked<typeof GetUserDetails>;

describe('useContextSetup', () => {
  beforeEach(() => {
    mockedBootstrapUtils.apiBootstrap.mockResolvedValue();
    mockGetUserDetails.apiGetUserDetails.mockResolvedValue({} as any);
  });

  test('useContextSetup', async () => {
    const testUserPersonalization: UserPersonalization = {
      gaClientId: '123',
      userLocation: {
        cityName: 'Portland',
        region: 1,
        stateAbbr: 'OR',
        zip: '12345',
      },
    };

    mockedFetchUtils.fetchGetUserPersonalization.mockReturnValue(
      testUserPersonalization,
    );

    const { result } = renderHook(() => useContextSetup(), {
      wrapper: ({ children }) => (
        <GlobalsContextProvider value={{}}>
          <AllThirdPartyScriptsContext>{children}</AllThirdPartyScriptsContext>
        </GlobalsContextProvider>
      ),
    });

    // it has expected initial state
    expect(result.current.locationString).toBe('');
    expect(result.current.userPersonalizationData).toBe(
      defaultUserPersonalizationData,
    );

    // it stores userPersonalization results
    waitFor(() =>
      expect(result.current.userPersonalizationData).toEqual(
        testUserPersonalization,
      ),
    );

    // it combines city and state after data is available
    waitFor(() => expect(result.current.locationString).toBe('Portland, OR'));
  });

  test('unknown location', async () => {
    const testUserPersonalization: UserPersonalization = {
      gaClientId: '123',
      userLocation: {
        cityName: null,
        region: null,
        stateAbbr: null,
        zip: null,
      },
    };

    mockedFetchUtils.fetchGetUserPersonalization.mockReturnValue(
      testUserPersonalization,
    );

    const { result } = renderHook(() => useContextSetup(), {
      wrapper: ({ children }) => (
        <GlobalsContextProvider value={{}}>
          <AllThirdPartyScriptsContext>{children}</AllThirdPartyScriptsContext>
        </GlobalsContextProvider>
      ),
    });

    // it has expected initial state
    expect(result.current.locationString).toBe('');
    expect(result.current.userPersonalizationData).toBe(
      defaultUserPersonalizationData,
    );

    // it stores userPersonalization results
    waitFor(() =>
      expect(result.current.userPersonalizationData).toEqual(
        testUserPersonalization,
      ),
    );

    // it returns empty string for unknown location
    expect(result.current.locationString).toBe('');
  });
});
