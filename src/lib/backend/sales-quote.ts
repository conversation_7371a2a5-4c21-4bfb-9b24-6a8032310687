import { RetrieveQuoteRequest } from '~/data/models/RetrieveQuoteRequest';
import { RetrieveQuoteResponse } from '~/data/models/RetrieveQuoteResponse';
import { SendQuoteRequest } from '~/data/models/SendQuoteRequest';
import { SendQuoteResponse } from '~/data/models/SendQuoteResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendUpdateSalesQuote(
  input: RetrieveQuoteRequest,
  extraQueryParams: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    RetrieveQuoteResponse,
    RetrieveQuoteRequest
  >({
    endpoint: '/v2/email-a-quote',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
  });
}

export async function backendSendQuote(
  input: SendQuoteRequest,
  extraQueryParams: Record<string, string>,
) {
  return await fetchWithErrorHandling<SendQuoteResponse, SendQuoteRequest>({
    endpoint: '/v2/email-a-quote',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
  });
}
