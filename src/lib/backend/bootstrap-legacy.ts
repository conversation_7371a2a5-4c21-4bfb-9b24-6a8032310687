import { IncomingMessage } from 'http';

import { UserPersonalization } from '~/data/models/UserPersonalization';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { URLS } from '~/lib/constants/urls';
import {
  fetchSetAuthorizationFunction,
  fetchSetAuthorizationToken,
  fetchSetSSOUseridFunction,
  fetchSetUrlBase,
  fetchSetUserPersonalization,
} from '~/lib/fetch-backend/legacy';
import logger from '~/lib/helpers/logger';
import {
  getFeatureBranchTicketName,
  getMockBranchTicketName,
  isLocal,
  isMockDeploy,
  isProductionDeploy,
} from '~/lib/utils/deploy';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

import { backendGetUserIdFromSSOToken } from './account/verify-user';
import { backendOauthTokenLegacy } from './oauth';

enum API {
  INTEGRATION = 'integration',
  LOCAL = 'local',
  MOCK = 'mock',
  PRODUCTION = 'production',
}

export function getBackendEnvVariables(): {
  backendEndpoint: string;
  clientId?: string;
  clientSecret?: string;
} {
  let pointTo = API.INTEGRATION;
  let integrationBranch = getFeatureBranchTicketName();
  const { mockBranch, buildNumber } = getMockBranchTicketName();

  if (isProductionDeploy()) {
    pointTo = API.PRODUCTION;
  } else if (isMockDeploy()) {
    pointTo = API.MOCK;
  } else {
    if (isLocal()) {
      const backend = process.env.STEER_BACKEND || 'mock';
      switch (backend) {
        case 'local':
          pointTo = API.LOCAL;
          break;
        case 'mock':
          pointTo = API.MOCK;
          break;
        case 'integration':
          pointTo = API.INTEGRATION;
          break;
        default:
          integrationBranch = backend;
      }
    }
  }

  if (pointTo === API.PRODUCTION) {
    return {
      backendEndpoint: URLS.MAIN_API_PRODUCTION,
      clientId: process.env.STEER_CLIENT_ID,
      clientSecret: process.env.STEER_CLIENT_SECRET_PROD,
    };
  }

  if (integrationBranch) {
    logger.info(
      `Pointing to Specific API Branch: ${URLS.MAIN_API_FEATURE(
        integrationBranch,
      )}`,
    );
    return {
      backendEndpoint: URLS.MAIN_API_FEATURE(integrationBranch),
      clientId: process.env.STEER_CLIENT_ID,
      clientSecret: process.env.STEER_CLIENT_SECRET_INTEGRATION,
    };
  }

  if (pointTo === API.LOCAL) {
    return {
      backendEndpoint: URLS.MAIN_API_LOCAL,
      clientId: process.env.STEER_CLIENT_ID,
      clientSecret: process.env.STEER_CLIENT_SECRET_MOCK,
    };
  }

  if (pointTo === API.MOCK) {
    return {
      backendEndpoint: URLS.MAIN_API_MOCK(mockBranch, buildNumber),
      clientId: process.env.STEER_CLIENT_ID,
      clientSecret: process.env.STEER_CLIENT_SECRET_MOCK,
    };
  }

  logger.info(`Pointing to Main API Integration: ${URLS.MAIN_API_INTEGRATION}`);
  return {
    backendEndpoint: URLS.MAIN_API_INTEGRATION,
    clientId: process.env.STEER_CLIENT_ID,
    clientSecret: process.env.STEER_CLIENT_SECRET_INTEGRATION,
  };
}

const { clientId, clientSecret, backendEndpoint } = getBackendEnvVariables();

async function authorizationFunction() {
  if (!clientId || !clientSecret) {
    throw new Error('Missing clientId or clientSecret');
  }

  const res = await backendOauthTokenLegacy({
    clientId,
    clientSecret,
  });

  if (res.isSuccess) {
    const clientToken = res.data.access_token;
    const expiresOn = new Date(Date.now() + res.data.expires_in * 1000);
    fetchSetAuthorizationToken(clientToken, expiresOn);
  }
}

async function ssoUserIdFunction(
  headerCookie: string,
): Promise<string | undefined> {
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }
  return ssoUserId;
}

export function backendBootstrap({
  request,
}: {
  request?: IncomingMessage & {
    query?: Partial<Record<string, string | string[]>>;
  };
} = {}) {
  fetchSetAuthorizationFunction(authorizationFunction);
  fetchSetSSOUseridFunction(ssoUserIdFunction);
  fetchSetUrlBase(backendEndpoint);

  if (request && request.query) {
    const userRegion = request.query.userRegion
      ? Number(request.query.userRegion)
      : null;
    const userZip =
      typeof request.query.userZip === 'string' ? request.query.userZip : null;

    if (userRegion || userZip) {
      const userPersonalization: UserPersonalization = {
        gaClientId: null,
        userLocation: {
          cityName: null,
          region: userRegion,
          stateAbbr: null,
          zip: userZip,
        },
      };

      fetchSetUserPersonalization(userPersonalization);
    }
  }
}
