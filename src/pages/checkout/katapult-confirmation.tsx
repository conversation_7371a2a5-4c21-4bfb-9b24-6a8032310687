import { GetServerSideProps } from 'next';
import nookies from 'nookies';

import KatapultConfirmationContainer from '~/components/pages/CheckoutPage/KatapultConfirmation/KatapultConfirmation';
import {
  SiteCartBillingApiResponse,
  SiteCartBillingResponse,
} from '~/data/models/SiteCartBillingResponse';
import {
  SiteCartShippingApiResponse,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetCartSummary } from '~/lib/backend/cart-summary';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { AsyncResponse } from '~/lib/fetch/index.types';
import logger from '~/lib/helpers/logger';
import { getUserIp } from '~/lib/utils/ip';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

interface Props {
  cartSummary: SiteCartSummary | null;
  customer_id: string | '';
  siteBilling: SiteCartBillingResponse | null;
  siteShipping: SiteCartShippingResponse | null;
  uid: string | '';
  userIp: string | null;
  zibby_id: string | '';
}

function KatapultConfirmation(props: Props) {
  return <KatapultConfirmationContainer {...props} />;
}

export const getServerSideProps: GetServerSideProps<Props> = async (
  context,
) => {
  backendBootstrap({ request: context.req });
  const userIp = getUserIp(context.req);
  const cookies = nookies.get(context);
  const storedCartId = cookies[COOKIES.CART_ID] ?? null;
  const headerCookie = context.req?.headers?.cookie ?? '';
  const userRegion = cookies[COOKIES.REGION];
  const userZip = cookies[COOKIES.ZIP];
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }
  const cartShippingResponse: AsyncResponse<SiteCartShippingApiResponse> =
    await backendGetCartShipping({ cartId: storedCartId });

  const cartBillingResponse: AsyncResponse<SiteCartBillingApiResponse> =
    await backendGetCartBilling({ cartId: storedCartId });

  const cartSummaryResponse: AsyncResponse<SiteCartSummaryResponse> =
    await backendGetCartSummary({
      id: storedCartId,
      query: {
        ssoUid: ssoUserId || '',
        userRegion,
        userZip,
      },
    });

  const customer_id = cookies[COOKIES.KATAPULT_CUSTOMER_COOKIE] ?? null;
  const zibby_id = cookies[COOKIES.KATAPULT_ZIBBY_ID_COOKIE] ?? null;
  logger.info(cookies[COOKIES.KATAPULT_ZIBBY_ID_COOKIE]);
  const uid = cookies[COOKIES.KATAPULT_UID_COOKIE] ?? null;
  // const { zibby_id, uid, customer_id } = getStringifiedParams(context.query);
  return {
    props: {
      cartSummary: cartSummaryResponse.isSuccess
        ? cartSummaryResponse.data.siteCart
        : null,

      customer_id,
      siteBilling: cartBillingResponse.isSuccess
        ? cartBillingResponse.data.siteCartBillingResponse
        : null,
      siteShipping: cartShippingResponse.isSuccess
        ? cartShippingResponse.data.siteCartShippingResponse
        : null,
      uid,
      userIp: userIp || null,
      zibby_id,
    },
  };
};
export default KatapultConfirmation;
