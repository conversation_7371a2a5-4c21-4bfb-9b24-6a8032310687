'use server';

import { unstable_cache } from 'next/cache';

import {
  backendBootstrapForAppRoutePromise,
  globalBootstrap,
} from '~/lib/backend/bootstrap';
import { backendBootstrap as backendBootstrapLegacy } from '~/lib/backend/bootstrap-legacy';
import {
  backendGetSiteGlobals,
  backendGetSiteMenu,
  backendGetSiteNotifications,
} from '~/lib/backend/site';
import { defaultSiteGlobalData } from '~/lib/constants/site-global';
import { defaultSiteMenuData } from '~/lib/constants/site-menu';
import logger from '~/lib/helpers/logger';
import { isOTSDeployment, isSimpleShopDeployment } from '~/lib/utils/deploy';

const defaultData = {
  isOTS: '0',
  isSimpleShop: '0',
  siteGlobals: defaultSiteGlobalData,
  siteMenu: defaultSiteMenuData,
  siteNotifications: undefined,
  vwoExperimentId1: process.env.VWO_EXPERIMENT_1,
  vwoExperimentId2: process.env.VWO_EXPERIMENT_2,
  vwoExperimentId3: process.env.VWO_EXPERIMENT_3,
  vwoExperimentId4: process.env.VWO_EXPERIMENT_4,
  vwoExperimentId5: process.env.VWO_EXPERIMENT_5,
};

const getSiteGlobals = unstable_cache(
  async () => {
    try {
      await backendBootstrapForAppRoutePromise;
      return backendGetSiteGlobals();
    } catch (error) {
      logger.error('getSiteGlobals error: ', error);
      throw error;
    }
  },
  ['siteGlobals'],
  {
    tags: ['siteGlobals'],
    revalidate: 28800, // 8 hours
  },
);
const getSiteMenu = unstable_cache(
  async ({ channel }) => {
    try {
      await backendBootstrapForAppRoutePromise;
      return backendGetSiteMenu({ channel });
    } catch (error) {
      logger.error('getSiteMenu error: ', error);
      throw error;
    }
  },
  [],
  {
    tags: ['siteMenu'],
    revalidate: 28800, // 8 hours
  },
);
const getSiteNotifications = unstable_cache(
  async () => {
    try {
      await backendBootstrapForAppRoutePromise;
      return backendGetSiteNotifications();
    } catch (error) {
      logger.error('getSiteNotifications error: ', error);
      throw error;
    }
  },
  [],
  {
    tags: ['siteNotifications'],
    revalidate: 28800, // 8 hours
  },
);

const isSimpleShop = isSimpleShopDeployment();
const isOTS = isOTSDeployment();

export async function getInitialProps() {
  backendBootstrapLegacy();
  await globalBootstrap();

  const channel = isSimpleShop ? 'widget' : undefined;
  const [siteGlobalsRes, siteMenuRes, siteNotificationsRes] = await Promise.all(
    [
      isOTS ? Promise.resolve(null) : getSiteGlobals(),
      getSiteMenu({ channel }),
      getSiteNotifications(),
    ],
  );

  const siteGlobals =
    siteGlobalsRes && siteGlobalsRes.isSuccess
      ? siteGlobalsRes.data.siteGlobals
      : undefined;

  const siteMenu = siteMenuRes.isSuccess ? siteMenuRes.data : undefined;

  const siteNotifications = siteNotificationsRes.isSuccess
    ? siteNotificationsRes.data
    : undefined;

  return {
    ...defaultData,
    isEPP: process.env.IS_EPP,
    isOTS: process.env.IS_OTS,
    isSimpleShop: process.env.IS_SIMPLE_SHOP,
    siteGlobals,
    siteMenu,
    siteNotifications,
    vwoExperimentId1: process.env.VWO_EXPERIMENT_1,
    vwoExperimentId2: process.env.VWO_EXPERIMENT_2,
    vwoExperimentId3: process.env.VWO_EXPERIMENT_3,
    vwoExperimentId4: process.env.VWO_EXPERIMENT_4,
    vwoExperimentId5: process.env.VWO_EXPERIMENT_5,
  };
}
