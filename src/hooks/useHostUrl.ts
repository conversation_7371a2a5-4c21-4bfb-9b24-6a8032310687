import { useEffect, useState } from 'react';

import {
  isEPPDeployment,
  isOTSDeployment,
  isProductionDeploy,
  isSimpleShopDeployment,
} from '~/lib/utils/deploy';

const isProduction = isProductionDeploy();
const isEPP = isEPPDeployment();
const isSimpleShop = isSimpleShopDeployment();
const isOTS = isOTSDeployment();

const defaultHostUrl =
  isEPP || isOTS || isSimpleShop
    ? ''
    : isProduction
      ? 'https://simpletire.com'
      : '';

function useHostUrl() {
  const [hostUrl, setHostUrl] = useState(defaultHostUrl);

  useEffect(() => {
    const hostUrl = window.location.origin;
    setHostUrl(hostUrl);
  }, []);

  return hostUrl;
}

export default useHostUrl;
