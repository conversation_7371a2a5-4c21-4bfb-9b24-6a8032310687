import { reviewListingMock } from '~/components/modules/ReviewListing/ReviewListing.mock';
import { GlobalsContextProvider } from '~/context/Globals.context';

import { default as HeaderComponent } from './Header';

export default {
  component: Header,
  title: 'SEO Landing/Review Listing/Header',
};

export function Header() {
  const { breadcrumbs, body, filters, title } = reviewListingMock;

  return (
    <GlobalsContextProvider value={{}}>
      <HeaderComponent
        breadcrumbs={breadcrumbs}
        body={body}
        title={title}
        filters={filters}
        selectedStars={[]}
        handleClickStar={() => {}}
        reviews={[]}
      />
    </GlobalsContextProvider>
  );
}
