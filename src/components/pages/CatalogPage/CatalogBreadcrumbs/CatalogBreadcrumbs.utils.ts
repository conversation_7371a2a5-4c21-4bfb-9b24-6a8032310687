import {
  SiteCatalogProductGroupItem,
  SiteCatalogProductGroupItemEnum,
} from '~/data/models/SiteCatalogProductGroupList';
import { SiteCatalogProductItem } from '~/data/models/SiteCatalogProductItem';
import { SiteCatalogSummaryTopPickItem } from '~/data/models/SiteCatalogSummaryTopPickItem';
import { SiteModuleBreadcrumbsItem } from '~/data/models/SiteModules';
import { ui } from '~/lib/utils/ui-dictionary';

import { CatalogTireSizeSchema } from './CatalogBreadCrumbs.types';

export function generateCatalogBreadcrumbsItems(
  size: string,
  siteCatalogBreadcrumbs: SiteModuleBreadcrumbsItem[],
) {
  let diameter;
  // try to match tire size like "24-8.50r14" or "24-8.50r14c"
  const rMatch = size.match(/r(\d{2})/);
  if (rMatch?.[1]) {
    diameter = rMatch[1];
  }
  // try to match tire size like "25-8.50-14", "25-8.50-14nhs" or "25-14"
  const nMatch = size.match(/(\d+)(?!.*\d)/);
  if (nMatch?.[0]) {
    diameter = nMatch[0];
  }
  const diameterNumber = Number(diameter);
  if (!diameterNumber) {
    return null;
  }
  const links = [
    {
      label: siteCatalogBreadcrumbs[0].label,
      url: `${siteCatalogBreadcrumbs[0].link.href}`,
    },
    {
      label: siteCatalogBreadcrumbs[1].label.replace('S', 's'),
      url: `${siteCatalogBreadcrumbs[1].link.href}`,
    },
  ];
  if (diameterNumber <= 22 && diameterNumber >= 14) {
    links.push({
      label: ui(`links.${diameterNumber}InchTires`),
      url: `/tire-sizes/diameter/${diameterNumber}-inch-tires`,
    });
  }
  links.push({
    label: `${siteCatalogBreadcrumbs[2].label} ${ui('catalog.header.tires')}`,
    url: `${siteCatalogBreadcrumbs[2].link.href}`,
  });
  return links;
}

export function generateCurationLinks(
  hostUrl: string,
  siteCatalogSummaryTopPicksList: Array<SiteCatalogSummaryTopPickItem>,
  siteCatalogProductsResultList: Array<
    SiteCatalogProductGroupItem | SiteCatalogProductItem
  >,
) {
  const links: Array<string> = [];
  siteCatalogSummaryTopPicksList.forEach((topPick) => {
    if (topPick.product) {
      links.push(`${hostUrl}${topPick.product.link.href}`.replace(/#.*$/, ''));
    }
  });
  siteCatalogProductsResultList.forEach((resultList) => {
    if (
      resultList.type ===
      SiteCatalogProductGroupItemEnum.SiteCatalogProductGroupItem
    ) {
      resultList.productList.forEach((product) => {
        links.push(`${hostUrl}${product.link.href}`.replace(/#.*$/, ''));
      });
    }
  });
  return links;
}

export function generateCatalogJSONLD(
  hostUrl: string,
  size: string,
  pageBreadCrumbItems: Array<{
    label: string;
    url: string;
  }>,
  curationLinks: Array<string>,
) {
  const jsonLDData: CatalogTireSizeSchema = {
    '@context': 'https://schema.org',
    '@id': `${hostUrl}/tire-sizes/${size}#webpage`, // Append #webpage to the URL
    '@type': 'CollectionPage',
    breadcrumb: {
      '@type': 'BreadcrumbList', // Nest the current breadcrumb schema
      itemListElement: pageBreadCrumbItems.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@id': item.url,
          name: item.label,
        },
      })),
    },
    inLanguage: 'en-US',
    isPartOf: {
      '@id': `${hostUrl}/#website`, // Append #website to the homepage URL
      '@type': 'WebSite',
      about: {
        '@type': 'Thing',
        name: 'SimpleTire',
        '@id': 'https://www.wikidata.org/wiki/Q118142384', // Static URL
      },
      name: 'SimpleTire',
      publisher: { '@id': `${hostUrl}/#organization` }, // Append #organization to the homepage URL
    },
    name: pageBreadCrumbItems[pageBreadCrumbItems.length - 1].label, // Uses the H1 of the page
    significantLink: curationLinks, // Insert all links from the curations without the parameters. These should all be Product Line links.
    url: `${hostUrl}/tire-sizes/${size}`,
  };

  if (pageBreadCrumbItems.length == 4) {
    jsonLDData.relatedLink = pageBreadCrumbItems[2].url; // Should be the diameter page of the breadcrumb structure
  }

  return jsonLDData;
}
