import { GlobalsContextProvider } from '~/context/Globals.context';
import { COLORS } from '~/lib/constants/colors';
import { THEME } from '~/lib/constants/theme';

import BreadcrumbsComponent from './Breadcrumbs';
import { BreadcrumbsItemProps } from './BreadcrumbsItem.types';

export default {
  component: BreadcrumbsComponent,
  title: 'Global/Breadcrumbs',
};

const mockNavigationData: BreadcrumbsItemProps[] = [
  {
    label: 'Home',
    url: '/',
  },
  {
    label: 'All Brands',
    url: '/all-brands',
  },
  {
    label: 'Continental',
    url: '/continental',
  },
  {
    label: 'ProContact Tire Line',
    url: '/continental/pro-contact',
  },
  {
    label: '215/55R16',
    url: '/continental/pro-contact/?size=215/55R16',
  },
];

export function Breadcrumbs({ theme }: { theme: THEME }) {
  const themeMap = {
    [THEME.DARK]: COLORS.GLOBAL.BLACK,
    [THEME.LIGHT]: COLORS.GLOBAL.WHITE,
    [THEME.ORANGE]: COLORS.GLOBAL.PRIMARY,
  };

  const backgroundColor = themeMap[theme];

  return (
    <GlobalsContextProvider value={{}}>
      <div css={{ backgroundColor, height: '100vh' }}>
        <BreadcrumbsComponent
          navigationItems={mockNavigationData}
          theme={theme}
        />
      </div>
    </GlobalsContextProvider>
  );
}

Breadcrumbs.args = {
  theme: THEME.LIGHT,
};

Breadcrumbs.argTypes = {
  disableJsonLD: { table: { disable: true } },
  navigationItems: { table: { disable: true } },
  theme: {
    control: 'select',
    options: [THEME.LIGHT, THEME.DARK, THEME.ORANGE],
  },
};
