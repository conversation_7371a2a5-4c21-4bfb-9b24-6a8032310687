'use client';

import { type ReactNode } from 'react';
import { SWRConfig } from 'swr';

import { GlobalsContextProvider } from './Globals.context';
import { WidgetConfigProvider } from './WidgetConfig.context';

interface Props {
  children: ReactNode;
  isOTS?: string;
  isSimpleShop?: string;
  vwoExperimentId1?: string;
  vwoExperimentId2?: string;
  vwoExperimentId3?: string;
  vwoExperimentId4?: string;
  vwoExperimentId5?: string;
}

// Container to wrap _app.tsx in context providers.
// Not all providers need to go here; only ones used throughout the app
function AppPriorityProviders({
  children,
  isSimpleShop,
  isOTS,
  vwoExperimentId2,
  vwoExperimentId1,
  vwoExperimentId3,
  vwoExperimentId4,
  vwoExperimentId5,
}: Props) {
  return (
    <SWRConfig value={{ revalidateOnFocus: false, shouldRetryOnError: false }}>
      <GlobalsContextProvider
        value={{
          isOTS,
          isSimpleShop,
          vwoExperimentId1,
          vwoExperimentId2,
          vwoExperimentId3,
          vwoExperimentId4,
          vwoExperimentId5,
        }}
      >
        <WidgetConfigProvider>{children}</WidgetConfigProvider>
      </GlobalsContextProvider>
    </SWRConfig>
  );
}

export default AppPriorityProviders;
