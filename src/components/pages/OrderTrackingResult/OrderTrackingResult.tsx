import { useState } from 'react';

import Button from '~/components/global/Button/Button';
import { CARS, CARS_KEYS } from '~/components/global/Car/CarDetails.constants';
import Grid from '~/components/global/Grid/Grid';
import GridItem from '~/components/global/Grid/GridItem';
import Icon from '~/components/global/Icon/Icon';
import { ICONS } from '~/components/global/Icon/Icon.constants';
import Loading from '~/components/global/Loading/Loading';
import Markdown from '~/components/global/Markdown/Markdown';
import Meta from '~/components/global/Meta/Meta';
import PageIllustration from '~/components/global/PageIllustration/PageIllustration';
import Toast from '~/components/global/Toast/Toast';
import BreakdownOT from '~/components/modules/Cart/CartSummaryModal/Breakdown/BreakdownOT';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { Order } from '~/data/models/Order';
import { OrderProduct } from '~/data/models/OrderProduct';
import { OrderTrackingInput } from '~/data/models/OrderTrackingInput';
import { ReturnRequestInput } from '~/data/models/ReturnRequestInput';
import { ShippingType } from '~/data/models/SiteCartShippingResponse';
import { SiteInstallLocation } from '~/data/models/SiteInstallLocation';
import useBreakpoints from '~/hooks/useBreakpoints';
import { BREAKPOINT_SIZES } from '~/lib/constants/breakpoints';
import { BUTTON_STYLE } from '~/lib/constants/buttons.types';
import { LINK_TYPES } from '~/lib/constants/links';
import { THEME } from '~/lib/constants/theme';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { isOTSDeployment } from '~/lib/utils/deploy';
import { formatDollars } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';

import { ShippingLocationProps } from '../CheckoutPage/CartSummaryPanel/ShippingLocation/ShippingLocation.types';
import OrderHeader from './OrderHeader/OrderHeader';
import OrderItem from './OrderItem/OrderItem';
import OrderStep from './OrderStep/OrderStep';
import OrderSummaryOT from './OrderTrackingOT';
import styles from './OrderTrackingResult.styles';
import { OrderStatus } from './OrderTrackingResult.types';
import {
  checkOrderStatus,
  getCancelOrderDescription,
  getExtraDeliveryContent,
  getOrderReceiptURL,
  getReturnDescription,
  getReturnInfoLinks,
} from './OrderTrackingResult.utils';

interface RequestType {
  type: string;
}

type ReturnReasonDataProps = OrderProduct & OrderTrackingInput;
type ReturnRequestProps = ReturnRequestInput & RequestType;

interface Props {
  customerServiceNumber: { display: string; value: string };
  emailSent: boolean;
  getReturnReasons: ({
    canCustomerReorder,
    canCustomerReturn,
    canCustomerCancelReturn,
    productId,
    image,
    name,
    quantity,
    zip,
    orderId,
  }: ReturnReasonDataProps) => void;
  installLocation?: SiteInstallLocation;
  isCustomerServiceEnabled: boolean;
  isDealerTire?: boolean;
  isLoadingCancelRequest: boolean;
  isLoadingReturnReasons: boolean;
  isSendingEmail: boolean;
  isSendingReturnOrCancelReq: boolean;
  isWalmartOrderTrackingPage?: boolean;
  orderCancelled: boolean;
  pdfDownloaded: boolean;
  returnOrCancelReqError: boolean;
  returnOrCancelReqSent: boolean;
  sendCancelRequest: ({
    orderId,
    zip,
    isDealerTire,
  }: OrderTrackingInput) => void;
  sendEmailReceipt: ({
    orderId,
    zip,
    isDealerTire,
  }: OrderTrackingInput) => void;
  sendReturnRequest: ({
    orderId,
    zip,
    productId,
    body,
  }: ReturnRequestProps) => void;
  setPDFdownloaded: (value: boolean) => void;
  showBackButton: boolean;
}

type OrderTrackingResultProps = Order & Props & OrderTrackingInput;

function OrderTrackingResult({
  customerServiceNumber,
  deliveryExpectedLabel,
  orderUuid,
  id,
  isCustomerServiceEnabled,
  orderProducts,
  shippingAddress,
  status,
  orderShippingStageList,
  maskedEmail,
  orderId,
  zip,
  returnOrCancelReqError,
  isLoadingReturnReasons,
  getReturnReasons,
  sendReturnRequest,
  isSendingReturnOrCancelReq,
  returnOrCancelReqSent,
  returnInitializedReasonId,
  showBackButton,
  isLoadingCancelRequest,
  canUserCancelOrder,
  sendCancelRequest,
  orderCancelled,
  deliveryExpectedContent,
  poNumber,
  resolveLoanId,
  isWalmartOrderTrackingPage,
  hasRoadsideAssistance,
  roadHazardCostInCents,
  savingTodayInCents,
  shippingCostInCents,
  subTotalInCents,
  totalInCents,
  estimatedStateFeesInCents,
  estimatedTaxInCents,
  estimatedFetTaxInCents,
  idMeVerifiedStatus,
  idMeDiscountInCents,
  idMeEligibleDiscountsInCents,
  coreShipOversizeFeePerTireInCents,
  coreShipHandlingPerTireInCents,
  oversizeTireQuantity,
  shipHandlingTireQuantity,
  coreOverSizedFeesApplied,
  coreShipHandlingFeesApplied,
  orderInstallerAppointment,
  paymentMethod,
  cardNumber,
  isDealerTire,
  shippingType,
  installationCostInCents,
  billingAddress,
  hasTrcOneYear,
  hasTrcThreeYear,
}: OrderTrackingResultProps) {
  const [toastMessageStatus, showToastMessage] = useState<boolean>(true);
  const [isOrderSummaryModalOpen, setIsOrderSummaryModalOpen] =
    useState<boolean>(false);
  const isOrderInReturnState = checkOrderStatus(status);
  const { isTireReplacementCoverageAvailable } =
    useUserPersonalizationContextSelector((v) => ({
      isTireReplacementCoverageAvailable: v.isTireReplacementCoverageAvailable,
    }));
  const isOTS = isOTSDeployment();

  const { bk, isMobileOrTablet } = useBreakpoints();
  const hasFreight = orderProducts.some(
    (product) => product.isShippingViaFreight || product.isOversizeTire,
  );
  const shippingCost =
    !shippingCostInCents || shippingCostInCents === 0
      ? 'Free'
      : formatDollars(shippingCostInCents);
  const shippingLocationProps: ShippingLocationProps | undefined =
    (shippingAddress && {
      addressLine1: shippingAddress.line1,
      addressLine2: shippingAddress.line2 || '',
      city: shippingAddress.cityName,
      isDealerTireGroup: isDealerTire && ShippingType.WAREHOUSE == shippingType,
      isMobileInstall: false,
      name:
        shippingAddress.firstName != ''
          ? shippingAddress.firstName + ' ' + shippingAddress.lastName
          : (shippingAddress.companyName ?? ''),
      onEdit: undefined,
      state: shippingAddress.stateAbbr,
      zip: shippingAddress.zip,
    }) ||
    undefined;
  const iconName = isOrderSummaryModalOpen
    ? ICONS.CHEVRON_SMALL_UP
    : ICONS.CHEVRON_SMALL_DOWN;
  const shippingDaysForAllTires = orderProducts?.reduce(
    (prev, current) => {
      const {
        shippingDays,
        brand: { label },
        name,
        isShippingViaFreight,
      } = current;
      if (!isShippingViaFreight) {
        prev.push({
          key: `${label} ${name}`,
          value: shippingDays ? shippingDays : '2 days',
        });
      }

      return prev;
    },
    [] as Array<{ key: string; value: string }>,
  );
  const isLorXLScreen =
    [BREAKPOINT_SIZES.L].includes(bk) || [BREAKPOINT_SIZES.XL].includes(bk);
  const receiptURL = getOrderReceiptURL({ orderId: orderUuid, zip });
  const addRudderStackDownloadEvent = async () => {
    rudderstackSendTrackEvent(RudderstackTrackEventName.FILE_DOWNLOADED, {
      link_url: receiptURL,
      link_text: ui('tracking.pdfDownload'),
      file_name: orderId + '.pdf',
    });
  };
  function renderReceipt() {
    const receiptURL = getOrderReceiptURL({ orderId: orderUuid, zip });
    return (
      <GridItem>
        <div css={styles.pdfWrapper}>
          <div css={styles.pdfButtonWrapper}>
            <Button
              as={LINK_TYPES.A}
              onClick={addRudderStackDownloadEvent}
              href={receiptURL}
              css={styles.button}
              theme={THEME.LIGHT}
              download={`${orderId}.pdf`}
            >
              {ui('tracking.pdfDownload')}
            </Button>
            {!isOTS && (
              <Button
                isDisabled={!canUserCancelOrder}
                css={styles.button}
                style={BUTTON_STYLE.OUTLINED}
                theme={THEME.LIGHT}
                onClick={() => sendCancelRequest({ orderId: orderUuid, zip })}
              >
                {ui('account.cancelOrder')}
              </Button>
            )}
            {isLoadingCancelRequest && (
              <div css={styles.loaderContainer}>
                <Loading />
              </div>
            )}
          </div>
          {isOTS ? (
            <div css={styles.cancelTextContainer}>
              <span css={[styles.cancelInfo]}>
                {getCancelOrderDescription()}
              </span>
            </div>
          ) : null}
          <div css={styles.cancelTextContainer}>
            <span css={[styles.cancelInfo]}>{getCancelOrderDescription()}</span>
          </div>
          <div css={styles.separatorLine} />
        </div>
      </GridItem>
    );
  }

  function renderOrderSummaryButton() {
    return (
      <GridItem>
        <div css={styles.pdfWrapper}>
          <div css={styles.pdfButtonWrapper}>
            <Button
              onClick={openOrderSummaryModel}
              css={styles.buttonOrderSummary}
              theme={THEME.LIGHT}
            >
              <span>{ui('tracking.orderSummaryButton')}</span>
              <Icon name={iconName} css={styles.chevronIcon} />
            </Button>
          </div>
        </div>
      </GridItem>
    );
  }
  const openOrderSummaryModel = async () => {
    if (isOrderSummaryModalOpen) {
      setIsOrderSummaryModalOpen(false);
    } else {
      setIsOrderSummaryModalOpen(true);
    }
  };

  function renderOrderDetails() {
    const receiptURL = getOrderReceiptURL({ orderId: orderUuid, zip });
    return (
      <GridItem
        css={[!isLorXLScreen && styles.orderInfoWrapper]}
        gridColumnL="8/13"
        gridColumnXL="8/12"
      >
        <ul>
          {orderProducts.map((item, i) => (
            <li
              css={[styles.orderItem, styles.seperator, styles.boxShadow]}
              key={i}
            >
              <OrderItem
                {...item}
                orderId={id}
                orderUuid={orderUuid}
                zip={String(zip)}
                isLoadingReturnReasons={isLoadingReturnReasons}
                getReturnReasons={getReturnReasons}
                sendReturnRequest={sendReturnRequest}
                isSendingReturnOrCancelReq={isSendingReturnOrCancelReq}
                returnOrCancelReqSent={returnOrCancelReqSent}
                poNumber={poNumber}
                resolveLoanId={resolveLoanId}
              />
            </li>
          ))}
        </ul>
        {isMobileOrTablet &&
          !isOrderSummaryModalOpen &&
          renderOrderSummaryButton()}
        {(!isMobileOrTablet || isOrderSummaryModalOpen) && (
          <div>
            <h6 css={styles.tiresTitle}>Breakdown</h6>
            <div css={styles.breakdown}>
              <BreakdownOT
                products={orderProducts}
                tireInstallationPrice={installationCostInCents}
                isOTS={isOTS}
                isRoadHazardChecked={
                  typeof roadHazardCostInCents !== 'undefined' &&
                  roadHazardCostInCents > 0
                }
                tireReplacementPrice={roadHazardCostInCents}
                hasRoadAssistance={hasRoadsideAssistance}
                hasTrcOneYear={hasTrcOneYear}
                hasTrcThreeYear={hasTrcThreeYear}
                isTireReplacementCoverageAvailable={
                  isTireReplacementCoverageAvailable
                }
              />
            </div>
            <OrderSummaryOT
              billingAddress={billingAddress}
              savingTodayInCents={savingTodayInCents ? savingTodayInCents : 0}
              shippingCostInCents={shippingCost}
              subTotalInCents={subTotalInCents ? subTotalInCents : 0}
              totalInCents={totalInCents}
              estimatedStateFeesInCents={estimatedStateFeesInCents}
              estimatedTaxInCents={
                estimatedTaxInCents ? estimatedTaxInCents : 0
              }
              estimatedFetTaxInCents={estimatedFetTaxInCents}
              idMeVerifiedStatus={idMeVerifiedStatus}
              idMeDiscountInCents={idMeDiscountInCents}
              idMeEligibleDiscountsInCents={idMeEligibleDiscountsInCents}
              displayRoadsideAssistance
              showShippingLocation={!hasFreight}
              shippingLocationData={shippingLocationProps}
              shippingDays={
                shippingDaysForAllTires.length > 0
                  ? shippingDaysForAllTires
                  : undefined
              }
              oversizedFeePerTireInCents={
                coreShipOversizeFeePerTireInCents || 0
              }
              shippingCostPerTireInCents={coreShipHandlingPerTireInCents || 0}
              numberOfOversizedTire={oversizeTireQuantity || 0}
              shipHandlingTireQuantity={shipHandlingTireQuantity || 0}
              coreOverSizedFeesApplied={coreOverSizedFeesApplied}
              coreShipHandlingFeesApplied={coreShipHandlingFeesApplied}
              paymentMethod={paymentMethod}
              cardNumber={cardNumber}
            />
          </div>
        )}
        {isMobileOrTablet &&
          isOrderSummaryModalOpen &&
          renderOrderSummaryButton()}
        {isMobileOrTablet && renderReceipt()}
        {returnOrCancelReqError && (
          <div css={styles.errorContainer}>
            <Toast
              isOpen={toastMessageStatus}
              onDismiss={() => showToastMessage(false)}
            >
              <Markdown>{ui('contactPage.message.error')}</Markdown>
            </Toast>
          </div>
        )}
        {!isMobileOrTablet && (
          <div>
            <div css={styles.pdfWrapper}>
              <div css={styles.pdfButtonWrapper}>
                <Button
                  as={LINK_TYPES.A}
                  isExternal
                  onClick={addRudderStackDownloadEvent}
                  href={receiptURL}
                  css={styles.button}
                  theme={THEME.LIGHT}
                  download={`${orderId}.pdf`}
                >
                  {ui('tracking.pdfDownload')}
                </Button>
                {!isOTS && (
                  <Button
                    isDisabled={!canUserCancelOrder}
                    css={styles.button}
                    style={BUTTON_STYLE.OUTLINED}
                    theme={THEME.LIGHT}
                    onClick={() =>
                      sendCancelRequest({
                        orderId: orderUuid,
                        zip,
                        isDealerTire,
                      })
                    }
                  >
                    {ui('account.cancelOrder')}
                  </Button>
                )}
              </div>
              <div css={styles.pdfText}>{ui('tracking.pdfDescription')}</div>
            </div>
            {!isDealerTire && !isOTS && (
              <div css={styles.additionalInfoWrapper}>
                <span css={styles.additionalInfo}>
                  {ui('tracking.returnInfoTitle')}
                </span>
                <span css={[styles.additionalInfo]}>
                  {getReturnInfoLinks()}
                </span>
              </div>
            )}
          </div>
        )}
      </GridItem>
    );
  }

  function renderOrderSteps() {
    return (
      <GridItem
        css={[
          !isLorXLScreen
            ? styles.orderTimelineWrapper
            : styles.orderTimelinePosition,
        ]}
        gridColumnL="3/8"
        gridColumnXL="3/8"
      >
        {isLorXLScreen && !isWalmartOrderTrackingPage && (
          <div css={styles.orderTimelineSeperator} />
        )}
        {orderShippingStageList.map((item, i) => (
          <OrderStep
            {...item}
            numberOfSteps={orderShippingStageList.length}
            orderInstallerAppointment={orderInstallerAppointment}
            shippingLocationData={shippingLocationProps}
            stepIndex={i}
            key={i}
          />
        ))}
        {returnOrCancelReqError && (
          <div css={styles.errorContainer}>
            <Toast
              isOpen={toastMessageStatus}
              onDismiss={() => showToastMessage(false)}
            >
              <Markdown>
                {orderCancelled
                  ? ui('account.orderCancelled')
                  : ui('contactPage.message.error')}
              </Markdown>
            </Toast>
          </div>
        )}
      </GridItem>
    );
  }
  return (
    <div>
      <Meta robots="noindex,nofollow" hasCanonical={false} />
      <Grid>
        <GridItem
          css={[
            styles.orderStatusWrapper,
            !isOrderInReturnState &&
              !isMobileOrTablet &&
              styles.returnContainer,
          ]}
        >
          <OrderHeader
            customerServiceNumber={customerServiceNumber}
            deliveryExpectedLabel={deliveryExpectedLabel}
            id={id}
            isCustomerServiceEnabled={isCustomerServiceEnabled}
            orderStatus={status}
            showBackButton={showBackButton}
            isOTS={isOTS}
          />
          {!isOTS &&
            deliveryExpectedContent &&
            !isMobileOrTablet &&
            getExtraDeliveryContent(deliveryExpectedContent)}
          {status === OrderStatus.RETURN_REQUESTED &&
            getReturnDescription(
              status,
              returnInitializedReasonId,
              maskedEmail ? maskedEmail : '',
            )}
        </GridItem>
        {status === OrderStatus.RETURN_INITIATED && (
          <GridItem gridColumnM="2/8" gridColumnL="3/12" gridColumnXL="4/12">
            {getReturnDescription(
              status,
              returnInitializedReasonId,
              maskedEmail ? maskedEmail : '',
            )}
          </GridItem>
        )}
        {!isOTS && deliveryExpectedContent && isMobileOrTablet && (
          <GridItem>
            {getExtraDeliveryContent(deliveryExpectedContent)}
          </GridItem>
        )}
        {isMobileOrTablet && !isWalmartOrderTrackingPage && (
          <GridItem isGrid>
            {renderOrderDetails()}
            {renderOrderSteps()}
          </GridItem>
        )}
        {!isMobileOrTablet && !isWalmartOrderTrackingPage && (
          <GridItem fullbleed isGrid>
            {renderOrderSteps()}
            {renderOrderDetails()}
          </GridItem>
        )}

        <PageIllustration carId={CARS[CARS_KEYS.COMMERCIAL]} />
      </Grid>
    </div>
  );
}
export default OrderTrackingResult;
