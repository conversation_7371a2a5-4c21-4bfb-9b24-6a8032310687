import { useMemo } from 'react';

import Breadcrumbs from '~/components/global/Breadcrumbs/Breadcrumbs';
import { useCatalogProductsContextSelector } from '~/context/CatalogProducts.context';
import { useCatalogSummaryContextSelector } from '~/context/CatalogSummary.context';
import { SiteCatalogProductGroupItemEnum } from '~/data/models/SiteCatalogProductGroupList';
import { mapArrayToBreadcrumbs } from '~/lib/utils/breadcrumbs';

import { generateCatalogBreadcrumbsItems } from './CatalogBreadcrumbs.utils';
import CatalogBreadcrumbsDataStructure from './CatalogBreadcrumbsDataStructure';

function CatalogBreadcrumbs() {
  const { size, siteCatalogBreadcrumbs } = useCatalogSummaryContextSelector(
    (v) => ({
      size: v.size,
      siteCatalogBreadcrumbs: v.siteCatalogSummary.siteCatalogBreadcrumbs,
    }),
  );
  const { isAdvancedView, siteCatalogProductsResultList } =
    useCatalogProductsContextSelector((v) => ({
      isAdvancedView: v.isAdvancedView,
      siteCatalogProductsResultList:
        v.siteCatalogProducts?.siteCatalogProductsResultList,
    }));
  const isGroupedProducts =
    !isAdvancedView &&
    siteCatalogProductsResultList?.[0]?.type ===
      SiteCatalogProductGroupItemEnum.SiteCatalogProductGroupItem;

  const pageBreadCrumbItems = useMemo(() => {
    if (!size || !isGroupedProducts || siteCatalogBreadcrumbs?.length !== 3) {
      return null;
    }
    return generateCatalogBreadcrumbsItems(size, siteCatalogBreadcrumbs);
  }, [size, isGroupedProducts, siteCatalogBreadcrumbs]);

  const pageBreadCrumbData = useMemo(() => {
    if (!pageBreadCrumbItems) {
      return null;
    }
    return mapArrayToBreadcrumbs(pageBreadCrumbItems);
  }, [pageBreadCrumbItems]);

  return (
    <div>
      {!!pageBreadCrumbData && (
        <Breadcrumbs navigationItems={pageBreadCrumbData} disableJsonLD />
      )}
      {pageBreadCrumbItems && (
        <CatalogBreadcrumbsDataStructure
          pageBreadCrumbItems={pageBreadCrumbItems}
        />
      )}
    </div>
  );
}

export default CatalogBreadcrumbs;
