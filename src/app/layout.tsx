import { Viewport } from 'next';
import dynamic from 'next/dynamic';
import Script from 'next/script';
import { ReactNode, Suspense } from 'react';

import { getInitialProps } from '~/actions/global';
import EmotionCacheProviderForAppRouter from '~/components/emotion/EmotionCacheProviderForAppRouter';
import EmotionGlobalForAppRouter from '~/components/emotion/EmotionGlobalForAppRouter';
import LinkMeta from '~/components/global/Meta/LinkMeta';
import AppServerContainer from '~/components/modules/App/App.server.container';
import DatadogInit from '~/components/modules/App/Datadog/DatadogInit';
import DOMErrorLogger from '~/components/modules/App/DOMErrorLogger/DOMErrorLogger';
import GlobalPriorityEffect from '~/components/modules/App/GlobalEffect/GlobalPriorityEffect';
import GlobalPriorityEffectForAppRoute from '~/components/modules/App/GlobalEffect/GlobalPriorityEffectForAppRoute';
import AppPriorityProviders from '~/context/AppPriorityProviders';
import AppProviders from '~/context/AppProviders';
import { GTM_CONSTANTS } from '~/lib/helpers/analytics';
import { getForterSnippet } from '~/lib/helpers/forter';
import { getGlobalErrorHandlerSnippet } from '~/lib/helpers/logger';
import { getVwoSnippet } from '~/lib/helpers/vwo';
import {
  getDefaultViewport,
  getFixedMetadata,
  getPageMetadata,
} from '~/lib/utils/metadata';

const GlobalEffect = dynamic(
  () => import('~/components/modules/App/GlobalEffect/GlobalEffect'),
);
const GlobalScript = dynamic(
  () => import('~/components/modules/App/GlobalScript/GlobalScript'),
);
const NavigationEvents = dynamic(
  () => import('~/components/modules/App/NavigationEvents/NavigationEvents'),
);

const { id: forterId, snippet: forterSnippet } = getForterSnippet();
const { id: vwoId, snippet: vwoSnippet } = getVwoSnippet();
const { id: globalErrorHandlerId, snippet: globalErrorHandlerSnippet } =
  getGlobalErrorHandlerSnippet();

export async function generateMetadata() {
  const fixedMeta = await getFixedMetadata();

  return {
    ...fixedMeta,
    ...getPageMetadata(),
  };
}

export const viewport: Viewport = getDefaultViewport();

async function RootLayout({ children }: { children: ReactNode }) {
  const {
    isSimpleShop,
    isOTS,
    isEPP,
    siteGlobals,
    siteMenu,
    siteNotifications,
    vwoExperimentId1,
    vwoExperimentId2,
    vwoExperimentId3,
    vwoExperimentId4,
    vwoExperimentId5,
  } = await getInitialProps();

  return (
    <html lang="en-US">
      <LinkMeta isOTS={isOTS} isSimpleShop={isSimpleShop} isEPP={isEPP} />
      <body>
        <EmotionCacheProviderForAppRouter>
          <EmotionGlobalForAppRouter />
          <DatadogInit />
          <DOMErrorLogger />
          <AppPriorityProviders
            isOTS={isOTS}
            isSimpleShop={isSimpleShop}
            vwoExperimentId1={vwoExperimentId1}
            vwoExperimentId2={vwoExperimentId2}
            vwoExperimentId3={vwoExperimentId3}
            vwoExperimentId4={vwoExperimentId4}
            vwoExperimentId5={vwoExperimentId5}
          >
            <GlobalPriorityEffectForAppRoute />
            <GlobalPriorityEffect isSimpleShop={isSimpleShop} isOTS={isOTS} />
            <AppProviders
              siteGlobalsContextValue={siteGlobals}
              siteMenuContextValue={siteMenu}
              siteNotificationContextValue={siteNotifications}
            >
              <Suspense fallback={null}>
                <GlobalScript />
              </Suspense>
              {/* wrap ApppContainer by a div which contains a `__next` id to make root node of page content is same as pages route. It will avoid setAppElement issue of ReactModal */}
              <div id="__next">
                <AppServerContainer>{children}</AppServerContainer>
              </div>
              <GlobalEffect />
            </AppProviders>
          </AppPriorityProviders>
        </EmotionCacheProviderForAppRouter>
        {/* This Suspense is a workaround to address `useSearchParams() should be wrapped in a suspense boundary` issue. We need to investigate if it's possible to avoid using useSearchParams in NavigationEvents, then we can totally remove this Suspense  */}
        <Suspense>
          <NavigationEvents />
        </Suspense>
        {/* Google Tag Manager (noscript) */}
        {!isOTS && !isEPP && !isSimpleShop && (
          <noscript
            dangerouslySetInnerHTML={{
              __html: `<iframe
              src="${GTM_CONSTANTS.GTM_SCRIPT}"
              height="0"
              width="0"
              style="display: none;visibility: hidden;"
            ></iframe>`,
            }}
          />
        )}
        {!isOTS && !isSimpleShop ? (
          <Script id={vwoId} strategy="beforeInteractive">
            {vwoSnippet}
          </Script>
        ) : null}
        <Script
          id={forterId}
          dangerouslySetInnerHTML={{ __html: forterSnippet }}
          strategy="beforeInteractive"
        />
        <Script
          id={globalErrorHandlerId}
          dangerouslySetInnerHTML={{ __html: globalErrorHandlerSnippet }}
          strategy="beforeInteractive"
        />
      </body>
    </html>
  );
}

export default RootLayout;
