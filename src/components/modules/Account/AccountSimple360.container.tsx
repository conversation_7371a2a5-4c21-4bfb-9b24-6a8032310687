'use client';

import useEffectOnlyOnce from '~/hooks/useEffectOnlyOnce';
import { AccountDetails } from '~/lib/constants/sso.types';

import AccountContainer from './Account.container';

function AccountSimple360Container({
  setCookie,
  ...rest
}: AccountDetails & { setCookie: () => void }) {
  useEffectOnlyOnce(
    () => {
      setCookie();
    },
    {},
    () => true,
  );
  return <AccountContainer {...rest} />;
}

export default AccountSimple360Container;
