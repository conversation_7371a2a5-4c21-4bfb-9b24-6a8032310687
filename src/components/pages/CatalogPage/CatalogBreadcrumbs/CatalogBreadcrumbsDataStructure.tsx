import { useMemo } from 'react';

import DataStructure from '~/components/global/DataStructure/DataStructure';
import { useCatalogProductsContextSelector } from '~/context/CatalogProducts.context';
import { useCatalogSummaryContextSelector } from '~/context/CatalogSummary.context';
import useHostUrl from '~/hooks/useHostUrl';

import {
  generateCatalogJSONLD,
  generateCurationLinks,
} from './CatalogBreadcrumbs.utils';

function CatalogBreadcrumbsDataStructure({
  pageBreadCrumbItems,
}: {
  pageBreadCrumbItems: Array<{
    label: string;
    url: string;
  }>;
}) {
  const { size, siteCatalogSummaryTopPicksList } =
    useCatalogSummaryContextSelector((v) => ({
      size: v.size,
      siteCatalogSummaryTopPicksList:
        v.siteCatalogSummary.siteCatalogSummaryTopPicksList,
    }));
  const siteCatalogProductsResultList = useCatalogProductsContextSelector(
    (v) => v.siteCatalogProducts?.siteCatalogProductsResultList,
  );
  const hostUrl = useHostUrl();

  const curationLinks = useMemo(() => {
    if (
      !hostUrl ||
      !siteCatalogSummaryTopPicksList ||
      !siteCatalogProductsResultList
    ) {
      return [];
    }
    return generateCurationLinks(
      hostUrl,
      siteCatalogSummaryTopPicksList,
      siteCatalogProductsResultList,
    );
  }, [hostUrl, siteCatalogSummaryTopPicksList, siteCatalogProductsResultList]);

  const dataStructure = useMemo(() => {
    if (!size || !hostUrl || !pageBreadCrumbItems) {
      return null;
    }
    return generateCatalogJSONLD(
      hostUrl,
      size,
      pageBreadCrumbItems,
      curationLinks,
    );
  }, [hostUrl, pageBreadCrumbItems, size, curationLinks]);

  return (
    dataStructure && <DataStructure id="tire-size" jsonLD={dataStructure} />
  );
}

export default CatalogBreadcrumbsDataStructure;
