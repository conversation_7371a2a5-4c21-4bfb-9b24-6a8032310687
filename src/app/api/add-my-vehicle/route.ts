import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

import { backendUpdateMyVehicle } from '~/lib/backend/account/my-vehicle';
import { backendGetCustomerId } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import logger from '~/lib/helpers/logger';

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const cookieStore = await cookies();
  const ssoToken = cookieStore.get(SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO)?.value;

  if (!ssoToken) {
    logger.error('No SSO token found in cookies');
    return new Response(null, { status: 401 });
  }

  const userId = await backendGetCustomerId(ssoToken);

  if (!userId) {
    logger.error('Failed to get customer ID from SSO token');
    return new Response(null, { status: 401 });
  }

  const body = await request.json();
  const res = await backendUpdateMyVehicle(String(userId), body);

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
