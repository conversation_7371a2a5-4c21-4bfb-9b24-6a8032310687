import { useEffect, useState } from 'react';

import DataStructure from '~/components/global/DataStructure/DataStructure';
import WithComponentErrorBoundary from '~/hocs/WithComponentErrorBoundary';
import useAllQueryParams from '~/hooks/useAllQueryParams';
import useHostUrl from '~/hooks/useHostUrl';

import { mapDataToLinkingData } from '../mappers/linkingData';
import { OutOfStockSchema } from '../ProductDetail.types';
import { generateOutOfStockSchema } from '../ProductDetail.utils';
import { useProductDetailDataContextSelector } from '../ProductDetailData.context';
import { useProductDetailOutOfStockContextSelector } from '../ProductDetailOutOfStock.context';
import { useIsPLA } from '../useProductDetailPageType.hooks';

function LinkDataStructure() {
  const hostUrl = useHostUrl();
  const isPLA = useIsPLA();
  const {
    performanceRating,
    reviewsList,
    reviewsSource,
    siteProductLine,
    siteProductLineAvailableSizeList,
  } = useProductDetailDataContextSelector((v) => ({
    callForPricing: v.productInfo.callForPricing,
    performanceRating: v.siteProductReviewsFromServerData.performanceRating,
    price: v.productInfo.price,
    reviewsList: v.siteProductReviewsFromServerData.reviewsList,
    reviewsSource: v.siteProductReviewsFromServerData.reviewsSource,
    siteProductLine: v.productInfo.siteProduct.siteProductLine,
    siteProductLineAvailableSizeList:
      v.productInfo.siteProduct.siteProductLineAvailableSizeList,
    size: v.productInfo.size,
    startingPrice: v.productInfo.startingPrice,
  }));
  const isOutOfStock = useProductDetailOutOfStockContextSelector(
    (v) => v.isOutOfStock,
  );
  const query = useAllQueryParams();
  const PLAOOS = isOutOfStock && isPLA;
  const [productId, setProductId] = useState<string>(
    Array.isArray(query.itemId) ? query.itemId[0] : query.itemId,
  );

  const linkingData = mapDataToLinkingData({
    hostUrl,
    isOutOfStock,
    isPLA,
    performanceRating,
    productId,
    query,
    reviewsList,
    reviewsSource,
    siteProductLine,
    siteProductLineAvailableSizeList,
  });

  useEffect(() => {
    // OPT-2318: fix a hydration issue which is caused by inconsistent url between server and client
    const hashParams = new URLSearchParams(window.location.hash);
    const itemId = hashParams.get('itemId');
    if (itemId) {
      setProductId((prevId) => {
        if (prevId !== itemId) {
          return itemId;
        }
        return prevId;
      });
    }
  }, []);
  return productId && linkingData && !PLAOOS ? (
    <DataStructure id="product-detail" jsonLD={linkingData} />
  ) : null;
}

function OutOfStockDataStructureFunc() {
  const {
    brandLabel,
    productId,
    productName,
    ratingQuantity,
    ratingValue,
    technicalSpecsDescription,
    technicalSpecsImage,
  } = useProductDetailDataContextSelector((v) => ({
    brandLabel: v.productInfo.brand.label,
    productId: v.productId,
    productName: v.productInfo.productName,
    ratingQuantity: v.productInfo.rating?.quantity ?? 0,
    ratingValue: v.productInfo.rating?.value ?? 0,
    technicalSpecsDescription: v.technicalSpecs?.description ?? '',
    technicalSpecsImage: v.technicalSpecs?.image?.src ?? '',
  }));

  const outOfStockDataStructure = generateOutOfStockSchema(
    brandLabel,
    technicalSpecsDescription,
    technicalSpecsImage,
    productName,
    ratingQuantity,
    ratingValue,
    productId,
  );

  return (
    <DataStructure
      id="product-deail-outof-stock"
      jsonLD={outOfStockDataStructure as OutOfStockSchema}
    />
  );
}

OutOfStockDataStructureFunc.displayName = 'OutOfStockDataStructure';

const OutOfStockDataStructure = WithComponentErrorBoundary(
  OutOfStockDataStructureFunc,
);

function IsOutOfStockDataStructureFunc() {
  const isOutOfStock = useProductDetailOutOfStockContextSelector(
    (v) => v.isOutOfStock,
  );
  return isOutOfStock ? <OutOfStockDataStructure /> : null;
}

IsOutOfStockDataStructureFunc.displayName = 'IsOutOfStockDataStructure';

const IsOutOfStockDataStructure = WithComponentErrorBoundary(
  IsOutOfStockDataStructureFunc,
);

function ProductDetailDataStructure() {
  return (
    <div>
      <LinkDataStructure />
      <IsOutOfStockDataStructure />
    </div>
  );
}

export default ProductDetailDataStructure;
