import { useState } from 'react';

import { OrderProduct } from '~/data/models/OrderProduct';
import { OrderTrackingInput } from '~/data/models/OrderTrackingInput';
import { ReturnRequestInput } from '~/data/models/ReturnRequestInput';
import { SiteImage } from '~/data/models/SiteImage';
import { ICON_IMAGE_TYPE } from '~/lib/backend/icon-image.types';

import {
  cancelReturnRequestMock,
  getReturnReasonsMock,
  orderBrandMock,
} from '../OrderTrackingResult.mock';
import OrderItem from './OrderItem';

interface RequestType {
  type: string;
}
type ReturnRequestProps = ReturnRequestInput & RequestType;
type ReturnReasonDataProps = OrderProduct & OrderTrackingInput;

export default {
  component: OrderItem,
  title: 'Tracking/OrderItem',
};

const itemImage = {
  altText: 'Tire sidewall',
  height: 800,
  src: 'https://images.simpletire.com/images/line-images/10695/10695-sidetread/bridgestone-dueler-a-t-revo-2.png',
  type: ICON_IMAGE_TYPE.IMAGE,
  width: 543,
} as SiteImage;

const styles = {
  wrapper: {
    margin: '30px auto',
    maxWidth: 400,
  },
};

interface OrderItemWithKnobsArgs {
  brand: string;
  canCustomerCancel: boolean;
  canCustomerReorder: boolean;
  canCustomerReturn: boolean;
  category: string;
  id: number;
  image: SiteImage;
  isLoadingReturnReasons: boolean;
  isSendingReturnOrCancelReq: boolean;
  line: string;
  name: string;
  price: number;
  quantity: number;
  sku: string;
  variant: string;
}

function OrderItemWithKnobsRender({
  name,
  quantity,
  id,
  price,
  canCustomerCancel,
  canCustomerReorder,
  canCustomerReturn,
  sku,
  variant,
  line,
  category,
  isSendingReturnOrCancelReq,
  isLoadingReturnReasons,
}: OrderItemWithKnobsArgs) {
  const [sendRequestParams, setStateForSendRequest] =
    useState<ReturnRequestProps>(cancelReturnRequestMock);
  const [getReasonsParams, setStateForReturnReasons] =
    useState<ReturnReasonDataProps>(getReturnReasonsMock);

  const zipCode = '12345';

  const sendReturnRequest = (data: ReturnRequestProps) => {
    setStateForSendRequest({
      ...sendRequestParams,
      ...data,
    });
  };

  const getReturnReasons = (data: ReturnReasonDataProps) => {
    setStateForReturnReasons({
      ...getReasonsParams,
      ...data,
    });
  };

  return (
    <div css={styles.wrapper}>
      <OrderItem
        canCustomerReorder={canCustomerReorder}
        canCustomerCancelReturn={canCustomerCancel}
        canCustomerReturn={canCustomerReturn}
        productId={id}
        category={category}
        sku={sku}
        line={line}
        variant={variant}
        image={itemImage}
        name={name}
        orderId={id}
        quantity={quantity}
        returnQuantity={quantity}
        zip={zipCode}
        isSendingReturnOrCancelReq={isSendingReturnOrCancelReq}
        returnOrCancelReqSent={isSendingReturnOrCancelReq}
        isLoadingReturnReasons={isLoadingReturnReasons}
        getReturnReasons={getReturnReasons}
        sendReturnRequest={sendReturnRequest}
        brand={orderBrandMock}
        price={price}
        extendedPrice={price}
        orderUuid={''}
      />
    </div>
  );
}

export const OrderItemWithKnobs = {
  args: {
    canCustomerCancel: true,
    canCustomerReorder: true,
    canCustomerReturn: true,
    category: 'All Season',
    id: 1234,
    isLoadingReturnReasons: false,
    isSendingReturnOrCancelReq: false,
    line: 'Dueler A/T Revo 2',
    name: 'Bridgestone Dueler A/T Revo 2 - P215/75R16 - 001311',
    price: 123,
    quantity: 2,
    sku: '001311',
    variant: 'P215/75R16',
  },
  argTypes: {
    getReturnReasons: { table: { disable: true } },
    orderId: { table: { disable: true } },
    returnOrCancelReqSent: { table: { disable: true } },
    sendReturnRequest: { table: { disable: true } },
    zip: { table: { disable: true } },
  },
  render: (args: OrderItemWithKnobsArgs) => {
    return <OrderItemWithKnobsRender {...args} />;
  },
};
