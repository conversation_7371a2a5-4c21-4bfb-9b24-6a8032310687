import { cache } from '@emotion/css';
import { CacheProvider, Global } from '@emotion/react';
import NextApp, { AppContext, AppInitialProps, AppProps } from 'next/app';
import dynamic from 'next/dynamic';
import { Suspense } from 'react';

import Meta from '~/components/global/Meta/Meta';
import AppServerContainer from '~/components/modules/App/App.server.container';
import DatadogInit from '~/components/modules/App/Datadog/DatadogInit';
import DOMErrorLogger from '~/components/modules/App/DOMErrorLogger/DOMErrorLogger';
import GlobalPriorityEffect from '~/components/modules/App/GlobalEffect/GlobalPriorityEffect';
import AppPriorityProviders from '~/context/AppPriorityProviders';
import AppProviders from '~/context/AppProviders';
import { SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import { SiteGlobals } from '~/data/models/SiteGlobals';
import { SiteMenuContextProps } from '~/data/models/SiteMenu';
import { SiteNotificationList } from '~/data/models/SiteNotificationsList';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import {
  backendGetSiteGlobals,
  backendGetSiteMenu,
  backendGetSiteNotifications,
} from '~/lib/backend/site';
import { global } from '~/styles/document/global.styles';

const GlobalEffect = dynamic(
  () => import('~/components/modules/App/GlobalEffect/GlobalEffect'),
);
const GlobalScript = dynamic(
  () => import('~/components/modules/App/GlobalScript/GlobalScript'),
);
const NavigationEvents = dynamic(
  () => import('~/components/modules/App/NavigationEvents/NavigationEvents'),
);

interface MyAppProps {
  serverData: {
    cartShipping?: SiteCartShippingResponse;
    cartSummary?: SiteCartSummary;
    isOTS?: string;
    isSimpleShop?: string;
    siteGlobals?: SiteGlobals;
    siteMenu?: SiteMenuContextProps;
    siteNotifications?: SiteNotificationList;
    vwoExperimentId1?: string;
    vwoExperimentId2?: string;
    vwoExperimentId3?: string;
    vwoExperimentId4?: string;
    vwoExperimentId5?: string;
  };
}

function MyApp({ Component, pageProps, serverData }: MyAppProps & AppProps) {
  const {
    isOTS,
    isSimpleShop,
    siteGlobals,
    siteMenu,
    siteNotifications,
    vwoExperimentId1,
    vwoExperimentId2,
    vwoExperimentId3,
    vwoExperimentId4,
    vwoExperimentId5,
  } = serverData;
  // STHD-7859. After remove cartSummary and cartShipping from getInitialProps, we still need to pass them to AppProviders to avoid payments page crashes when jump from Pirelli widget. These data are also fetched during SSR of payments page, so get them from pageProps and forward them to AppProviders.
  const cartSummary = pageProps?.siteCartSummary?.siteCart;
  const cartShipping = pageProps?.siteCartShipping;

  if (pageProps?.errorStatusCode === 410 && pageProps?.isBot) {
    // FND-1586 render an empty page without any global component like Nav and Footer for bots if it's a 410 error.
    return null;
  }

  return (
    <CacheProvider value={cache}>
      <Global styles={global} />
      <DatadogInit />
      <DOMErrorLogger />
      <AppPriorityProviders
        isOTS={isOTS}
        isSimpleShop={isSimpleShop}
        vwoExperimentId1={vwoExperimentId1}
        vwoExperimentId2={vwoExperimentId2}
        vwoExperimentId3={vwoExperimentId3}
        vwoExperimentId4={vwoExperimentId4}
        vwoExperimentId5={vwoExperimentId5}
      >
        <GlobalPriorityEffect isSimpleShop={isSimpleShop} isOTS={isOTS} />
        <AppProviders
          siteGlobalsContextValue={siteGlobals}
          siteMenuContextValue={siteMenu}
          siteNotificationContextValue={siteNotifications}
          cartSummary={cartSummary}
          cartShipping={cartShipping}
        >
          <Suspense fallback={null}>
            <GlobalScript />
          </Suspense>
          <Meta />
          <AppServerContainer>
            <Component {...pageProps} />
          </AppServerContainer>
          <Suspense fallback={null}>
            <GlobalEffect />
          </Suspense>
        </AppProviders>
      </AppPriorityProviders>
      <Suspense fallback={null}>
        <NavigationEvents />
      </Suspense>
    </CacheProvider>
  );
}

MyApp.getInitialProps = async (
  appContext: AppContext,
): Promise<MyAppProps & AppInitialProps> => {
  // calls page's `getInitialProps` and fills `appProps.pageProps`
  const appProps = await NextApp.getInitialProps(appContext);

  backendBootstrap();

  const {
    ctx: { query },
  } = appContext;

  const channel = query.channel ? (query.channel as string) : '';
  const [siteGlobalsRes, siteMenuRes, siteNotificationsRes] = await Promise.all(
    [
      backendGetSiteGlobals(),
      backendGetSiteMenu({ channel: channel ? channel : '' }),
      backendGetSiteNotifications(),
    ],
  );

  const siteGlobals = siteGlobalsRes.isSuccess
    ? siteGlobalsRes.data.siteGlobals
    : undefined;
  const siteMenu = siteMenuRes.isSuccess ? siteMenuRes.data : undefined;

  const siteNotifications = siteNotificationsRes.isSuccess
    ? siteNotificationsRes.data
    : undefined;

  const finalProps = {
    ...appProps,
    serverData: {
      isEPP: process.env.IS_EPP,
      isOTS: process.env.IS_OTS,
      isSimpleShop: process.env.IS_SIMPLE_SHOP,
      siteGlobals: process.env.IS_OTS === '1' ? undefined : siteGlobals,
      siteMenu,
      siteNotifications,
      vwoExperimentId1: process.env.VWO_EXPERIMENT_1,
      vwoExperimentId2: process.env.VWO_EXPERIMENT_2,
      vwoExperimentId3: process.env.VWO_EXPERIMENT_3,
      vwoExperimentId4: process.env.VWO_EXPERIMENT_4,
      vwoExperimentId5: process.env.VWO_EXPERIMENT_5,
    },
  };

  return finalProps;
};

export default MyApp;
