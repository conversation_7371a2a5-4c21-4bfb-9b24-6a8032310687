'use client';

import { parseCookies } from 'nookies';
import { useCallback, useState } from 'react';

import Button from '~/components/global/Button/Button';
import Loading from '~/components/global/Loading/Loading';
import InstallationAnimation from '~/components/modules/PDP/Installation/InstallationIllustration';
import { OrderConfirmationRequest } from '~/data/models/OrderConfirmationRequest';
import { SiteCartOrderResponse } from '~/data/models/SiteCartOrderResponse';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { useApiDataWithDefault } from '~/hooks/useApiDataWithDefault';
import useRouter from '~/hooks/useRouter';
import { apiConfirmOrder } from '~/lib/api/checkout/order-confirmation';
import { COOKIES } from '~/lib/constants/cookies';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { THEME } from '~/lib/constants/theme';
import logger from '~/lib/helpers/logger';
import { ui } from '~/lib/utils/ui-dictionary';

import Footer from '../Footer/Footer';
import Header from '../Header/Header';
import OrderSummarySection from '../OrderConfirmation/OrderSummarySection/OrderSummarySection';
import { ServerData as OrderSummaryServerData } from '../OrderSummary/OrderSummary.types';
import styles from './ConfirmYourOrder.styles';

export interface ServerData extends OrderSummaryServerData {
  cartId: string;
  orderIdEncypt: string;
  siteCartOrder: SiteCartOrderResponse;
}

function ConfirmYourOrder({
  siteBilling,
  siteShipping,
  siteCartOrder,
  siteCartSummaryData,
  cartId,
  orderIdEncypt,
}: ServerData) {
  const router = useRouter();
  const { data: cartSummaryData } =
    useApiDataWithDefault<SiteCartSummaryResponse | null>({
      defaultData: siteCartSummaryData ?? null,
      endpoint: '/cart-summary',
      includeAuthorization: true,
      includeUserRegion: true,
      includeUserTime: true,
      includeUserZip: true,
      query: { id: cartId },
    });
  const { order } = siteCartOrder;
  const [orderData, setOrderData] = useState(order);
  const [confirmButtonLoading, setConfirmButtonLoading] = useState(false);

  const cookies = parseCookies();
  const forterToken = cookies[COOKIES.FORTER] || null;

  const confirmOrder = useCallback(
    async (orderConfirmationRequest: OrderConfirmationRequest) => {
      const res = await apiConfirmOrder({
        input: orderConfirmationRequest,
      });
      if (res.isSuccess) {
        logger.info(res.data.order);
        setOrderData(res.data.order);
        setConfirmButtonLoading(false);
      } else {
        setConfirmButtonLoading(false);
        logger.info('logger');
      }
    },
    [],
  );
  logger.info({
    orderId: orderIdEncypt,
    forterToken,
  });
  const handleContinue = async () => {
    if (!order.forterVerificationRequired) {
      setConfirmButtonLoading(true);
      router.push(ROUTE_MAP[ROUTES.HOME]);
    }
    setConfirmButtonLoading(true);
    if (forterToken) {
      const requestBody: OrderConfirmationRequest = {
        orderId: orderIdEncypt,
        forterToken,
      };
      await confirmOrder(requestBody);
    } else {
      setConfirmButtonLoading(true);
    }
  };
  return (
    <div>
      <div css={styles.orangeContent}>
        <div css={styles.widthWrapper}>
          <Header onlyWhiteLogo />
          <div css={styles.content}>
            <p css={styles.order}>
              {ui('checkout.orderConfirm.header.order', {
                orderId: orderData.orderId,
              })}
            </p>
            <p css={styles.order}>
              {ui('checkout.orderConfirm.header.orderDate', {
                orderDate: orderData.orderDate ?? '',
              })}
            </p>
            <h2 css={styles.orderConfirm}>
              {orderData.forterVerificationRequired
                ? ui('checkout.orderConfirm.header.confirmYourOrder')
                : ui('checkout.orderConfirm.header.confirmedOrder')}
            </h2>
            <p css={styles.orderText}>
              {orderData.forterVerificationRequired
                ? ui('checkout.orderConfirm.header.confirmText')
                : ui('checkout.orderConfirm.header.confirmedText')}
            </p>
          </div>
        </div>

        <InstallationAnimation
          aria-label={ui('pdp.installation.illustrationAltText')}
          css={[styles.illustration]}
          onOrangeBg
          animateIn
          hideShop
        />
      </div>

      <div>
        <OrderSummarySection
          siteShipping={siteShipping}
          siteBilling={siteBilling}
          order={order}
          siteCartSummaryData={cartSummaryData}
          isConfirmOrderForterPage
        />
      </div>
      {orderData.forterVerificationRequired ? (
        <div>
          <h2 css={styles.orderConfirmText}>
            {ui('checkout.orderConfirm.header.confirmYourOrderText')}
          </h2>
          <a
            css={styles.continue}
            role="button"
            tabIndex={-1}
            onClick={handleContinue}
          >
            <Button
              theme={THEME.LIGHT}
              css={[styles.button]}
              data-test-id="new-checkout"
            >
              {confirmButtonLoading ? (
                <Loading
                  theme={THEME.DARK}
                  customContainerStyles={styles.loading}
                />
              ) : (
                <span>Confirm Order</span>
              )}
            </Button>
          </a>
        </div>
      ) : (
        <a
          css={styles.continue}
          role="button"
          tabIndex={-1}
          onClick={handleContinue}
        >
          <Button
            theme={THEME.LIGHT}
            css={[styles.button]}
            data-test-id="new-checkout"
          >
            Continue Shopping
          </Button>
        </a>
      )}

      <Footer />
    </div>
  );
}

export default ConfirmYourOrder;
