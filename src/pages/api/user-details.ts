import { NextApiRequest, NextApiResponse } from 'next';

import {
  backendGetUserIdFromSSOToken,
  extractTokenFromCookie,
} from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';

const getUserDetailsApi = async (
  request: NextApiRequest,
  response: NextApiResponse,
) => {
  backendBootstrap({ request });
  const cookie = request?.headers.cookie ? request.headers.cookie : '';

  // get the token from cookie
  const ssoTokenInCookie = await extractTokenFromCookie(String(cookie));
  if (ssoTokenInCookie) {
    const tokenResponse = await backendGetUserIdFromSSOToken(ssoTokenInCookie);

    if (tokenResponse.isSuccess) {
      const result = { ...tokenResponse.data };
      delete result.password;
      return response.json(result);
    }

    return response.status(400).end();
  } else {
    return response.status(204).end();
  }
};

export default getUserDetailsApi;
