import { GetServerSideProps } from 'next';
import nookies from 'nookies';

import ConfirmYourOrder, {
  ServerData,
} from '~/components/pages/CheckoutPage/ConfirmYourOrder/ConfirmYourOrder';
import WithErrorPageHandling, {
  PageResponse,
} from '~/hocs/WithPageErrorHandling';
import { backendGetUserIdFromSSOToken } from '~/lib/backend/account/verify-user';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import { backendGetCartSummary } from '~/lib/backend/cart-summary';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { backendGetConfirmOrderLegacy } from '~/lib/backend/checkout/order-confirmation';
import { COOKIES } from '~/lib/constants/cookies';
import { SSO_COOKIE_CONSTANTS } from '~/lib/constants/sso';
import { getStringifiedParams } from '~/lib/utils/routes';
import { extractSpecificSSOCookie } from '~/lib/utils/sso';

function ConfirmOrder(props: ServerData) {
  return <ConfirmYourOrder {...props} />;
}

export const getServerSideProps: GetServerSideProps<
  PageResponse<ServerData>
> = async (context) => {
  backendBootstrap({ request: context.req });
  const queryParams = getStringifiedParams(context.query);
  const { orderId } = queryParams;

  const headerCookie = context.req?.headers?.cookie ?? '';
  let ssoUserId;
  const ssoToken = await extractSpecificSSOCookie(
    SSO_COOKIE_CONSTANTS.SIMPLETIRE_SSO,
    String(headerCookie),
  );
  const cookies = nookies.get(context);
  const userRegion = cookies[COOKIES.REGION];
  const userZip = cookies[COOKIES.ZIP];
  if (ssoToken) {
    const response = await backendGetUserIdFromSSOToken(ssoToken);
    if (response.isSuccess) {
      const uid = response.data?.uid;
      if (uid) {
        ssoUserId = uid;
      }
    }
  }

  if (!orderId) {
    return { props: { errorStatusCode: 400 } };
  }

  const [cartOrder] = await Promise.all([
    backendGetConfirmOrderLegacy({ query: queryParams }),
  ]);
  if (!cartOrder.isSuccess) {
    const errorStatusCode = !cartOrder.isSuccess
      ? cartOrder.error?.statusCode
      : 500;

    context.res.statusCode = errorStatusCode;
    return { props: { errorStatusCode } };
  }

  if (!cartOrder.data) {
    return { props: { errorStatusCode: 404 } };
  }
  const cartId = cartOrder.data.order.cartId ?? '';
  const [cartShippingResponse, cartBillingResponse, cartSummaryResponse] =
    await Promise.all([
      backendGetCartShipping({ cartId }, context.req),
      backendGetCartBilling({ cartId }, context.req),
      backendGetCartSummary(
        { id: cartId, query: { ssoUid: ssoUserId || '', userRegion, userZip } },
        context.req,
      ),
    ]);

  return {
    props: {
      cartId,
      orderIdEncypt: orderId,
      simplePayConfirmData: null,
      siteBilling: cartBillingResponse.isSuccess
        ? cartBillingResponse.data.siteCartBillingResponse
        : null,
      siteCartOrder: cartOrder.data,
      siteCartSummary: cartSummaryResponse,
      siteShipping: cartShippingResponse.isSuccess
        ? cartShippingResponse.data.siteCartShippingResponse
        : null,
    },
  };
};

export default WithErrorPageHandling(ConfirmOrder);
