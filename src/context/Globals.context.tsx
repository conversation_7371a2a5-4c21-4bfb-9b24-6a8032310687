'use client';

import { ReactNode } from 'react';

import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';

interface GlobalsContextProps {
  isOTS?: string;
  isSimpleShop?: string;
  vwoExperimentId1?: string;
  vwoExperimentId2?: string;
  vwoExperimentId3?: string;
  vwoExperimentId4?: string;
  vwoExperimentId5?: string;
}

const GlobalsContext = createContext<GlobalsContextProps>();

interface Props {
  children: ReactNode;
  value: GlobalsContextProps;
}

export function GlobalsContextProvider({
  children,
  value: {
    isOTS,
    isSimpleShop,
    vwoExperimentId1,
    vwoExperimentId2,
    vwoExperimentId3,
    vwoExperimentId4,
    vwoExperimentId5,
  },
}: Props) {
  return (
    <GlobalsContext.Provider
      value={{
        isOTS,
        isSimpleShop,
        vwoExperimentId1,
        vwoExperimentId2,
        vwoExperimentId3,
        vwoExperimentId4,
        vwoExperimentId5,
      }}
    >
      {children}
    </GlobalsContext.Provider>
  );
}

export const useGlobalsContextSelector = <SelectedValue,>(
  selector: Selector<GlobalsContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<GlobalsContextProps, SelectedValue>(
    GlobalsContext,
    selector,
    equalCompareFn,
  );
